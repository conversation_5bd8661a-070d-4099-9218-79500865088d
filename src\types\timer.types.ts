/**
 * Type definitions for timer-related data structures
 */

export type TimerType = 'pomodoro';
export type PomodoroPhase = 'work' | 'break' | 'longBreak';
export type TimerStatus = 'idle' | 'running' | 'paused' | 'completed';

export interface TimerSettings {
  pomodoro_work_duration: number; // minutes
  pomodoro_break_duration: number; // minutes
  pomodoro_long_break_duration: number; // minutes
  pomodoro_sessions_until_long_break: number;
  countdown_default_duration: number; // minutes
  auto_start_breaks: boolean;
  auto_start_work: boolean;
  sound_enabled: boolean;
  notification_enabled: boolean;
}

export interface TimerSession {
  id: string;
  user_id: string;
  timer_type: TimerType;
  duration: number; // seconds
  completed_duration: number; // seconds
  started_at: string;
  completed_at?: string;
  paused_duration?: number; // seconds
  was_completed: boolean;
  notes?: string;
}

export interface PomodoroSession extends TimerSession {
  phase: PomodoroPhase;
  session_number: number;
  total_work_sessions: number;
  total_break_sessions: number;
}

export interface TimerStats {
  total_sessions: number;
  total_focus_time: number; // minutes
  total_break_time: number; // minutes
  completed_sessions: number;
  average_session_length: number; // minutes
  longest_session: number; // minutes
  sessions_today: number;
  focus_time_today: number; // minutes
  sessions_this_week: number;
  focus_time_this_week: number; // minutes
  sessions_this_month: number;
  focus_time_this_month: number; // minutes
  most_productive_hour: number; // 0-23
  most_productive_day: string; // 'monday', 'tuesday', etc.
}

export interface TimerPreferences {
  default_timer_type: TimerType;
  show_timer_in_notification: boolean;
  keep_screen_on: boolean;
  dim_screen_during_break: boolean;
  show_progress_ring: boolean;
  show_session_count: boolean;
  vibrate_on_completion: boolean;
  play_sound_on_completion: boolean;
  completion_sound: string;
}

export interface BreathingExercise {
  id: string;
  name: string;
  description: string;
  duration: number; // seconds
  inhale_duration: number; // seconds
  hold_duration: number; // seconds
  exhale_duration: number; // seconds
  cycles: number;
  background_sound?: string;
  instructions: string[];
}

export interface BreathingSession {
  id: string;
  user_id: string;
  exercise_id: string;
  duration: number; // seconds
  completed_cycles: number;
  started_at: string;
  completed_at?: string;
  was_completed: boolean;
  mood_before?: number; // 1-5
  mood_after?: number; // 1-5
  notes?: string;
}

export interface TimerNotification {
  id: string;
  title: string;
  body: string;
  type: 'session_complete' | 'break_complete' | 'reminder';
  scheduled_for: string;
  is_sent: boolean;
  timer_session_id?: string;
}

export interface FocusGoal {
  id: string;
  user_id: string;
  goal_type: 'daily' | 'weekly' | 'monthly';
  target_minutes: number;
  current_minutes: number;
  start_date: string;
  end_date: string;
  is_active: boolean;
  is_completed: boolean;
}

export interface TimerAnalytics {
  total_focus_sessions: number;
  total_focus_time: number; // minutes
  average_session_length: number; // minutes
  completion_rate: number; // percentage
  most_productive_time: {
    hour: number;
    day: string;
  };
  weekly_progress: {
    date: string;
    sessions: number;
    focus_time: number;
  }[];
  monthly_progress: {
    month: string;
    sessions: number;
    focus_time: number;
  }[];
  timer_type_breakdown: {
    type: TimerType;
    sessions: number;
    percentage: number;
  }[];
  focus_streaks: {
    current_streak: number; // days
    longest_streak: number; // days
  };
}

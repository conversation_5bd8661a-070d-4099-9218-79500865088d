import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { HabitFormData, HabitTimeRange } from '../../types/habit.types';

interface HabitFormStep4Props {
  formData: HabitFormData;
  onEdit: (step: number) => void;
}

const HabitFormStep4: React.FC<HabitFormStep4Props> = ({ formData, onEdit }) => {
  const getCategoryLabel = (category: string) => {
    const categories = {
      health: 'Health & Fitness',
      productivity: 'Productivity',
      learning: 'Learning & Growth',
      mindfulness: 'Mindfulness',
      social: 'Social & Relationships',
      creativity: 'Creativity',
      finance: 'Finance',
      other: 'Other',
    };
    return categories[category as keyof typeof categories] || 'Other';
  };

  const getFrequencyLabel = (frequency: string) => {
    const frequencies = {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      custom: 'Custom',
    };
    return frequencies[frequency as keyof typeof frequencies] || 'Daily';
  };

  const getUnitLabel = (unit: string) => {
    const units = {
      times: 'times',
      minutes: 'minutes',
      hours: 'hours',
      pages: 'pages',
      glasses: 'glasses',
      steps: 'steps',
      kilometers: 'kilometers',
      miles: 'miles',
      pounds: 'pounds',
      kilograms: 'kilograms',
      other: 'other',
    };
    return units[unit as keyof typeof units] || 'times';
  };

  const formatTimeRange = (timeRange?: HabitTimeRange) => {
    if (!timeRange) return 'Not specified';
    return `${timeRange.startTime} - ${timeRange.endTime}`;
  };

  return (
    <ScrollView className="space-y-6">
      {/* Step Title */}
      <View className="mb-6 text-center">
        <Text className="mb-2 text-2xl font-bold text-gray-900 dark:text-gray-100">
          Review & Confirm
        </Text>
        <Text className="text-gray-600 dark:text-gray-400">
          Review your habit details before creating it
        </Text>
      </View>

      {/* Basic Information */}
      <View className="rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800">
        <View className="mb-3 flex-row items-center justify-between">
          <Text className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Basic Information
          </Text>
          <TouchableOpacity
            onPress={() => onEdit(1)}
            className="rounded-lg bg-blue-100 px-3 py-1 dark:bg-blue-900"
          >
            <Text className="text-sm font-medium text-blue-600 dark:text-blue-400">Edit</Text>
          </TouchableOpacity>
        </View>

        <View className="space-y-3">
          <View className="flex-row items-center">
            <View
              className="mr-3 h-4 w-4 rounded-full"
              style={{ backgroundColor: formData.color }}
            />
            <Text className="flex-1 text-xl font-bold text-gray-900 dark:text-gray-100">
              {formData.name}
            </Text>
          </View>

          <View>
            <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">Category</Text>
            <Text className="text-gray-900 dark:text-gray-100">
              {getCategoryLabel(formData.category)}
            </Text>
          </View>

          <View>
            <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">Purpose</Text>
            <Text className="text-gray-900 dark:text-gray-100">{formData.purpose}</Text>
          </View>

          {formData.description && (
            <View>
              <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Description
              </Text>
              <Text className="text-gray-900 dark:text-gray-100">{formData.description}</Text>
            </View>
          )}
        </View>
      </View>

      {/* Schedule & Frequency */}
      <View className="rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800">
        <View className="mb-3 flex-row items-center justify-between">
          <Text className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Schedule & Frequency
          </Text>
          <TouchableOpacity
            onPress={() => onEdit(2)}
            className="rounded-lg bg-blue-100 px-3 py-1 dark:bg-blue-900"
          >
            <Text className="text-sm font-medium text-blue-600 dark:text-blue-400">Edit</Text>
          </TouchableOpacity>
        </View>

        <View className="space-y-3">
          <View className="flex-row justify-between">
            <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">Frequency</Text>
            <Text className="text-gray-900 dark:text-gray-100">
              {getFrequencyLabel(formData.frequency)}
            </Text>
          </View>

          <View className="flex-row justify-between">
            <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Target Goal
            </Text>
            <Text className="text-gray-900 dark:text-gray-100">
              {formData.target_count} {getUnitLabel(formData.unit)}
            </Text>
          </View>

          <View className="flex-row justify-between">
            <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">Time Range</Text>
            <Text className="text-gray-900 dark:text-gray-100">
              {formatTimeRange(formData.timeRange)}
            </Text>
          </View>

          <View className="flex-row justify-between">
            <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">Reminders</Text>
            <Text className="text-gray-900 dark:text-gray-100">
              {formData.reminder_enabled
                ? `Enabled${formData.reminder_time ? ` at ${formData.reminder_time}` : ''}`
                : 'Disabled'}
            </Text>
          </View>
        </View>
      </View>

      {/* Sub-tasks */}
      <View className="rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800">
        <View className="mb-3 flex-row items-center justify-between">
          <Text className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Sub-tasks ({formData.subTasks.length})
          </Text>
          <TouchableOpacity
            onPress={() => onEdit(3)}
            className="rounded-lg bg-blue-100 px-3 py-1 dark:bg-blue-900"
          >
            <Text className="text-sm font-medium text-blue-600 dark:text-blue-400">Edit</Text>
          </TouchableOpacity>
        </View>

        {formData.subTasks.length === 0 ? (
          <Text className="py-4 text-center text-gray-600 dark:text-gray-400">
            No sub-tasks added
          </Text>
        ) : (
          <View className="space-y-2">
            {formData.subTasks.map((task, index) => (
              <View key={task.id} className="flex-row items-start">
                <Text className="mr-2 font-medium text-blue-500">{index + 1}.</Text>
                <View className="flex-1">
                  <Text className="font-medium text-gray-900 dark:text-gray-100">{task.name}</Text>
                  {task.description && (
                    <Text className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                      {task.description}
                    </Text>
                  )}
                </View>
              </View>
            ))}
          </View>
        )}
      </View>

      {/* Success Message */}
      <View className="rounded-xl bg-green-50 p-4 dark:bg-green-900">
        <View className="flex-row items-center">
          <Ionicons name="checkmark-circle" size={24} color="#10B981" />
          <Text className="ml-2 font-medium text-green-800 dark:text-green-200">
            Ready to Create!
          </Text>
        </View>
        <Text className="mt-2 text-sm text-green-700 dark:text-green-300">
          Your habit is ready to be created. You can always edit these details later from your habit
          list.
        </Text>
      </View>

      {/* Progress Indicator */}
      <View className="mt-6 rounded-xl bg-blue-50 p-4 dark:bg-blue-900">
        <View className="flex-row items-center">
          <View className="mr-3 h-3 w-3 rounded-full bg-blue-500" />
          <Text className="font-medium text-blue-700 dark:text-blue-300">
            Step 4 of 4: Review & Confirm
          </Text>
        </View>
        <Text className="mt-2 text-sm text-blue-600 dark:text-blue-400">
          Perfect! Click &quot;Create Habit&quot; to start building your new habit.
        </Text>
      </View>
    </ScrollView>
  );
};

export default HabitFormStep4;

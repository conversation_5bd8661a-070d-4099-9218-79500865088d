import uuid from 'react-native-uuid';

/**
 * Generate a UUID v4 string compatible with React Native
 * This replaces crypto.getRandomValues() which is not available in React Native
 */
export const generateUUID = (): string => {
  return uuid.v4() as string;
};

/**
 * Validate if a string is a valid UUID
 */
export const isValidUUID = (str: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
};

/**
 * Generate a short UUID (first 8 characters) for display purposes
 */
export const generateShortUUID = (): string => {
  return generateUUID().substring(0, 8);
};

import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useAppStore, useHabitStore, useUserStore } from '../../src/stores';

export default function HomeScreen() {
  const router = useRouter();
  const { currentUser, loadUser } = useUserStore();
  const { habits, loadHabits } = useHabitStore();
  const { setActiveTab } = useAppStore();

  useEffect(() => {
    setActiveTab('home');

    const initializeHome = async () => {
      if (!currentUser) {
        await loadUser();
      }

      if (currentUser) {
        await loadHabits(currentUser.id);
      }
    };

    initializeHome();
  }, [currentUser, loadHabits, loadUser, setActiveTab]);

  const today = new Date().toISOString().split('T')[0];
  const activeHabits = habits.filter(habit => habit.is_active);
  const completedToday = activeHabits.filter(habit =>
    habit.completions?.some(completion => completion.completed_date === today)
  ).length;

  const handleCreateHabit = () => {
    router.push('/habit-form');
  };

  const handleStartTimer = () => {
    router.push('/(tabs)/pomodoro');
  };

  const handleViewHabits = () => {
    router.push('/(tabs)/habits');
  };

  return (
    <ScrollView className="flex-1 bg-gray-50 dark:bg-gray-900">
      <StatusBar style="auto" />
      {/* Header */}
      <View className="bg-white px-6 pb-6 pt-12 shadow-sm dark:bg-gray-800">
        <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Welcome back{currentUser ? `, ${currentUser.name.split(' ')[0]}` : ''}!
        </Text>
        <Text className="mt-1 text-gray-600 dark:text-gray-400">
          {new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
        </Text>
      </View>

      {/* Today's Progress */}
      <View className="mx-6 mt-6">
        <View className="rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Today&apos;s Progress
            </Text>
            <Ionicons name="trending-up" size={24} color="#3B82F6" />
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-1">
              <Text className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                {completedToday}/{activeHabits.length}
              </Text>
              <Text className="text-gray-600 dark:text-gray-400">Habits completed</Text>
            </View>

            <View className="h-20 w-20 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
              <Text className="text-lg font-bold text-blue-600">
                {activeHabits.length > 0
                  ? Math.round((completedToday / activeHabits.length) * 100)
                  : 0}
                %
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View className="mx-6 mt-6">
        <Text className="mb-4 text-lg font-semibold text-gray-900 dark:text-gray-100">
          Quick Actions
        </Text>

        <View className="flex-row">
          <TouchableOpacity
            className="mr-2 flex-1 items-center rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800"
            onPress={handleCreateHabit}
          >
            <View className="mb-2 h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
              <Ionicons name="add" size={24} color="#10B981" />
            </View>
            <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">Add Habit</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="mx-2 flex-1 items-center rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800"
            onPress={handleStartTimer}
          >
            <View className="mb-2 h-12 w-12 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/20">
              <Ionicons name="timer" size={24} color="#8B5CF6" />
            </View>
            <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">Pomodoro</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="ml-2 flex-1 items-center rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800"
            onPress={handleViewHabits}
          >
            <View className="mb-2 h-12 w-12 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
              <Ionicons name="stats-chart" size={24} color="#F59E0B" />
            </View>
            <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">
              View Habits
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Recent Habits */}
      <View className="mx-6 mb-6 mt-6">
        <Text className="mb-4 text-lg font-semibold text-gray-900">Today&apos;s Habits</Text>

        {activeHabits.length === 0 ? (
          <View className="items-center rounded-xl bg-white p-6 shadow-sm">
            <Ionicons name="leaf-outline" size={48} color="#9CA3AF" />
            <Text className="mt-2 text-center text-gray-600">
              No habits yet. Start building your routine!
            </Text>
          </View>
        ) : (
          <View className="space-y-3">
            {activeHabits.slice(0, 5).map(habit => {
              const isCompletedToday = habit.completions?.some(
                completion => completion.completed_date === today
              );

              return (
                <View
                  key={habit.id}
                  className="flex-row items-center rounded-xl bg-white p-4 shadow-sm"
                >
                  <View
                    className="mr-3 h-4 w-4 rounded-full"
                    style={{ backgroundColor: habit.color }}
                  />
                  <View className="flex-1">
                    <Text className="font-medium text-gray-900">{habit.name}</Text>
                    <Text className="text-sm text-gray-600">{habit.category}</Text>
                  </View>
                  <View
                    className={`h-6 w-6 items-center justify-center rounded-full border-2 ${isCompletedToday ? 'border-green-500 bg-green-500' : 'border-gray-300'
                      }`}
                  >
                    {isCompletedToday && <Ionicons name="checkmark" size={16} color="white" />}
                  </View>
                </View>
              );
            })}
          </View>
        )}
      </View>
    </ScrollView>
  );
}

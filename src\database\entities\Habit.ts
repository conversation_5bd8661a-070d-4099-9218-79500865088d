import {
  Column,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum HabitFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  CUSTOM = 'custom',
}

export enum HabitCategory {
  HEALTH = 'health',
  FITNESS = 'fitness',
  PRODUCTIVITY = 'productivity',
  LEARNING = 'learning',
  MINDFULNESS = 'mindfulness',
  SOCIAL = 'social',
  CREATIVE = 'creative',
  OTHER = 'other',
}

@Entity('habits')
export class Habit {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 50, default: HabitCategory.OTHER })
  category: HabitCategory;

  @Column({ type: 'varchar', length: 20, default: HabitFrequency.DAILY })
  frequency: HabitFrequency;

  @Column({ type: 'integer', default: 1 })
  target_count: number;

  @Column({ type: 'varchar', length: 20, default: 'times' })
  unit: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  icon?: string;

  @Column({ type: 'varchar', length: 7, default: '#3B82F6' })
  color: string;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Column({ type: 'integer', default: 0 })
  sort_order: number;

  @Column({ type: 'json', nullable: true })
  reminder_times?: string[];

  @Column({ type: 'boolean', default: false })
  notifications_enabled: boolean;

  @Column({ type: 'text', nullable: true })
  purpose?: string;

  @Column({ type: 'json', nullable: true })
  time_range?: {
    startTime: string;
    endTime: string;
    isFlexible: boolean;
  };

  @Column({ type: 'json', nullable: true })
  sub_tasks?: {
    id: string;
    name: string;
    description: string;
    order: number;
  }[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid' })
  user_id: string;

  @ManyToOne('User', 'habits', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: any;

  @OneToMany('HabitCompletion', 'habit')
  completions: any[];

  @OneToMany('HabitStreak', 'habit')
  streaks: any[];
}

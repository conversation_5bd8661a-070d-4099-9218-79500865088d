import { useEffect } from 'react';
import { useHabitStore, useUserStore } from '../stores';

/**
 * Custom hook for habit management
 * Provides convenient access to habit operations and state
 */
export const useHabits = () => {
  const { currentUser } = useUserStore();
  const {
    habits,
    selectedHabit,
    isLoading,
    error,
    loadHabits,
    createHabit,
    updateHabit,
    deleteHabit,
    setSelectedHabit,
    completeHabit,
    uncompleteHabit,
    getCurrentStreak,
    getLongestStreak,
    clearError,
  } = useHabitStore();

  // Auto-load habits when user changes
  useEffect(() => {
    if (currentUser) {
      loadHabits(currentUser.id);
    }
  }, [currentUser, loadHabits]);

  // Helper functions
  const getActiveHabits = () => habits.filter(habit => habit.is_active);

  const getHabitById = (id: string) => habits.find(habit => habit.id === id);

  const isHabitCompletedToday = (habitId: string) => {
    const today = new Date().toISOString().split('T')[0];
    const habit = getHabitById(habitId);
    return habit?.completions?.some(completion => completion.completed_date === today) || false;
  };

  const getTodayCompletionCount = () => {
    const today = new Date().toISOString().split('T')[0];
    return getActiveHabits().filter(habit =>
      habit.completions?.some(completion => completion.completed_date === today)
    ).length;
  };

  const toggleHabitCompletion = async (habitId: string) => {
    const today = new Date().toISOString().split('T')[0];

    if (isHabitCompletedToday(habitId)) {
      await uncompleteHabit(habitId, today);
    } else {
      await completeHabit(habitId, today);
    }
  };

  return {
    // State
    habits,
    activeHabits: getActiveHabits(),
    selectedHabit,
    isLoading,
    error,

    // Actions
    loadHabits,
    createHabit,
    updateHabit,
    deleteHabit,
    setSelectedHabit,
    completeHabit,
    uncompleteHabit,
    toggleHabitCompletion,
    getCurrentStreak,
    getLongestStreak,
    clearError,

    // Bulk operations
    deleteHabits: async (habitIds: string[]) => {
      try {
        await Promise.all(habitIds.map(id => deleteHabit(id)));
      } catch (error) {
        // Failed to delete habits
        throw error;
      }
    },

    archiveHabits: async (habitIds: string[]) => {
      try {
        // Archive functionality would need to be implemented in the store
        // For now, we'll just delete them
        await Promise.all(habitIds.map(id => deleteHabit(id)));
      } catch (error) {
        // Failed to archive habits
        throw error;
      }
    },

    reorderHabits: async (habitIds: string[]) => {
      try {
        // Reorder functionality would need to be implemented in the store
        // For now, this is a placeholder
        // Reordering habits
      } catch (error) {
        // Failed to reorder habits
        throw error;
      }
    },

    // Helper functions
    getHabitById,
    isHabitCompletedToday,
    getTodayCompletionCount,
  };
};

export default useHabits;

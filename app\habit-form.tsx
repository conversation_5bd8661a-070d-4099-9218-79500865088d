import { useLocalSearchParams, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { Alert, View } from 'react-native';
import HabitFormWizard from '../src/components/habits/HabitFormWizard';
import { useHabits } from '../src/hooks/useHabits';
import { useUserStore } from '../src/stores';
import { HabitFormData } from '../src/types/habit.types';

export default function HabitFormScreen() {
  const router = useRouter();
  const { habitId } = useLocalSearchParams<{ habitId?: string }>();
  const { currentUser } = useUserStore();
  const { createHabit, updateHabit, getHabitById, isLoading } = useHabits();

  const isEditing = !!habitId;
  const existingHabit = isEditing ? getHabitById(habitId) : null;

  const handleSubmit = async (formData: HabitFormData) => {
    if (!currentUser) {
      Alert.alert('Error', 'You must be logged in to create habits');
      return;
    }

    try {
      if (isEditing && existingHabit) {
        // Update existing habit
        await updateHabit(existingHabit.id, {
          name: formData.name,
          description: formData.description,
          category: formData.category as any,
          frequency: formData.frequency as any,
          target_count: formData.target_count,
          unit: formData.unit as any,
          color: formData.color,
          reminder_times: formData.reminder_time ? [formData.reminder_time] : [],
          notifications_enabled: formData.reminder_enabled || false,
          purpose: formData.purpose,
          time_range: formData.timeRange,
          sub_tasks: formData.subTasks.map(task => ({
            id: task.id || Date.now().toString(),
            name: task.name,
            description: task.description,
            order: task.order,
          })),
        });

        Alert.alert('Success', 'Habit updated successfully!', [
          { text: 'OK', onPress: () => router.back() },
        ]);
      } else {
        // Create new habit
        await createHabit({
          name: formData.name,
          description: formData.description,
          category: formData.category as any,
          frequency: formData.frequency as any,
          target_count: formData.target_count,
          unit: formData.unit as any,
          color: formData.color,
          reminder_times: formData.reminder_time ? [formData.reminder_time] : [],
          notifications_enabled: formData.reminder_enabled || false,
          purpose: formData.purpose,
          time_range: formData.timeRange,
          sub_tasks: formData.subTasks.map(task => ({
            id: task.id || Date.now().toString(),
            name: task.name,
            description: task.description,
            order: task.order,
          })),
          user_id: currentUser.id,
          is_active: true,
          sort_order: 0,
        });

        Alert.alert('Success', 'Habit created successfully!', [
          { text: 'OK', onPress: () => router.back() },
        ]);
      }
    } catch (error) {
      // Error saving habit
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to save habit');
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const getInitialData = (): Partial<HabitFormData> | undefined => {
    if (!existingHabit) return undefined;

    return {
      name: existingHabit.name,
      description: existingHabit.description,
      category: existingHabit.category as any,
      frequency: existingHabit.frequency as any,
      target_count: existingHabit.target_count,
      unit: existingHabit.unit as any,
      color: existingHabit.color,
      reminder_time: existingHabit.reminder_times?.[0],
      reminder_enabled: existingHabit.notifications_enabled,
      purpose: existingHabit.purpose || '',
      timeRange: existingHabit.time_range,
      subTasks:
        existingHabit.sub_tasks?.map(task => ({
          id: task.id,
          name: task.name,
          description: task.description,
          completed: false,
          order: task.order,
        })) || [],
    };
  };

  return (
    <View className="flex-1">
      <StatusBar style="auto" />
      <HabitFormWizard
        initialData={getInitialData()}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isEditing={isEditing}
        isLoading={isLoading}
      />
    </View>
  );
}

# WinArc: Habit Tracker 🎯

**Build consistent daily routines through habit formation, time management, and
mindfulness practices.**

WinArc is a comprehensive habit tracking mobile application built with
Expo-managed React Native, designed to help users develop and maintain positive
daily habits through an intuitive interface, powerful tracking capabilities, and
integrated productivity tools.

## ✨ Features

### 🎯 Core Habit Management

- **Hierarchical Habit Structure**: Create habits with name, purpose, time
  ranges, and sub-tasks
- **Smart Habit Creation**: Create habits with flexible scheduling (daily,
  weekly, custom intervals)
- **Sub-task Management**: Break down habits into manageable sub-tasks with
  descriptions
- **Purpose-Driven Design**: Define the purpose and motivation behind each habit
- **Progress Tracking**: Visual progress indicators with streak counters and
  completion rates
- **Habit Categories**: Organize habits by categories (Health, Productivity,
  Learning, etc.)
- **Completion History**: Detailed history with notes and completion timestamps

### ⏱️ Enhanced Pomodoro Timer

- **Customizable Sessions**: Adjustable work/break intervals with preset
  templates
- **Session Tracking**: Track completed Pomodoro sessions with productivity
  insights
- **Background Notifications**: Continue sessions even when app is backgrounded
- **Integration**: Link Pomodoro sessions to specific habits for enhanced
  tracking

### 📅 Event Countdown System

- **Event Tracking**: Track important dates like birthdays, anniversaries, and
  holidays
- **Days Remaining**: Visual countdown showing days remaining until events
- **Recurring Events**: Support for annual recurring events
- **Event Categories**: Organize events by type (Personal, Work, Holidays, etc.)
- **Quick Overview**: See all upcoming events at a glance

### 🧘 Breathing Exercises & Mindfulness

- **Guided Breathing**: Various breathing techniques (4-7-8, Box Breathing,
  etc.)
- **Visual Guides**: Animated breathing guides with customizable pace
- **Session Tracking**: Track mindfulness sessions as part of daily habits
- **Stress Relief**: Quick access breathing exercises for stress management

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and npm/yarn
- **Expo CLI**: `npm install -g @expo/cli`
- **Mobile Development Environment**:
  - iOS: Xcode and iOS Simulator (macOS only)
  - Android: Android Studio and Android Emulator
  - Physical Device: Expo Go app for testing

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd WinArc
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start the development server**

   ```bash
   npx expo start
   ```

4. **Run on your preferred platform**

   ```bash
   # iOS Simulator (macOS only)
   npx expo start --ios

   # Android Emulator
   npx expo start --android

   # Web browser (for testing)
   npx expo start --web
   ```

### Development Workflow

```bash
# Code quality and formatting
npm run lint              # Run ESLint
npm run lint:fix          # Fix ESLint issues automatically
npm run format            # Format code with Prettier
npm run format:check      # Check code formatting
npm run type-check        # Run TypeScript type checking
npm run dev:tools         # Run all quality checks

# Development
npm start                 # Start Expo development server
npm run android          # Run on Android
npm run ios              # Run on iOS
npm run web              # Run on web
```

## 🏗️ Technology Stack

### **Core Framework**

- **Expo SDK ~53.0**: Managed React Native development platform
- **React Native 0.79+**: Cross-platform mobile app framework
- **TypeScript**: Full type safety and enhanced developer experience

### **UI & Styling**

- **NativeWind v4**: Tailwind CSS for React Native with compile-time
  optimizations
- **Expo Router**: File-based navigation system
- **React Native Reanimated**: Smooth animations and interactions

### **Data & State Management**

- **Expo SQLite**: Local database storage
- **TypeORM**: Object-relational mapping for database operations
- **Zustand**: Lightweight state management

### **Development Tools**

- **ESLint**: Code linting with Expo-compatible rules
- **Prettier**: Code formatting with Tailwind CSS support
- **TypeScript**: Static type checking
- **VS Code**: Optimized development environment

## 📱 App Architecture

### **File-based Routing Structure**

```
app/
├── (tabs)/              # Tab Navigator
│   ├── _layout.tsx      # Tab layout configuration
│   ├── index.tsx        # Home/Habits Screen
│   ├── timer.tsx        # Timer Screen (Pomodoro + Event Countdown)
│   ├── analytics.tsx    # Analytics Screen
│   └── settings.tsx     # Settings Screen
├── _layout.tsx          # Root layout with theme provider
├── index.tsx            # App entry point
├── onboarding.tsx       # User onboarding flow
├── habit-detail.tsx     # Habit detail and editing
└── habit-form.tsx       # Habit creation form
```

### **Source Code Organization**

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Generic UI components
│   ├── habits/         # Habit-specific components
│   ├── timer/          # Timer-specific components
│   └── events/         # Event countdown components
├── database/           # TypeORM entities and repositories
│   ├── entities/       # Database entity definitions
│   ├── repositories/   # Data access layer
│   └── DatabaseService.ts # Database service singleton
├── stores/             # Zustand state management
├── hooks/              # Custom React hooks
├── services/           # Business logic services
├── styles/             # Global styles and themes
├── types/              # TypeScript type definitions
└── utils/              # Utility functions and helpers
```

## 📊 Current Development Status

### ✅ Phase 1 - Foundation (Completed)

- [x] Expo-managed React Native project setup
- [x] NativeWind v4 styling system
- [x] TypeORM database architecture with Android compatibility
- [x] Zustand state management
- [x] Expo Router navigation
- [x] Core habit CRUD operations
- [x] Hierarchical habit structure with sub-tasks and purpose fields
- [x] Pomodoro timer functionality
- [x] Event countdown system
- [x] Light/dark theme support

### 🚧 Phase 2 - Enhanced Features (In Progress)

- [x] Advanced habit features (subtasks, purpose, time ranges)
- [ ] Breathing exercises and mindfulness tools
- [ ] Enhanced analytics and insights
- [ ] Smart notifications and reminders
- [ ] Data export and backup
- [ ] Time range picker with DateTimePicker integration

### 📋 Phase 3 - Advanced Features (Planned)

- [ ] Social features and accountability
- [ ] Wellness integration and mood tracking
- [ ] Brain dump and journaling
- [ ] Habit-based alarm system

## 📖 Documentation

Comprehensive documentation is available in the `docs/` directory:

- **[📋 Features Specification](./docs/FEATURES.md)**: Detailed feature
  requirements and user stories
- **[🏗️ Project Structure](./docs/PROJECT_STRUCTURE.md)**: Architecture and
  folder organization
- **[⚡ Technology Stack](./docs/TECH_STACK.md)**: Technology choices and
  implementation details
- **[🗺️ Development Roadmap](./docs/ROADMAP.md)**: Development phases and
  milestones

## 🤝 Contributing

This project follows modern React Native and Expo development practices:

1. **Code Style**: Follow ESLint and Prettier configurations
2. **TypeScript**: Maintain full type coverage
3. **Testing**: Write tests for critical functionality
4. **Documentation**: Update docs when adding features
5. **Git Workflow**: Use conventional commits and feature branches

## 📄 License

This project is private and proprietary. All rights reserved.

---

**Built with ❤️ using Expo and React Native**

For questions, issues, or feature requests, please refer to the project
documentation or contact the development team.

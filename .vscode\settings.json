{
  // Editor settings
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit",
    "source.sortMembers": "explicit"
  },
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,

  // File associations
  "files.associations": {
    "*.tsx": "typescriptreact",
    "*.ts": "typescript"
  },

  // Auto save
  "files.autoSave": "onFocusChange",

  // Emmet
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },

  // TypeScript
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",

  // ESLint
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": false,

  // Prettier
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,

  // React Native specific
  "reactNativeTools.showUserTips": false,
  "reactNativeTools.projectRoot": "./",

  // File explorer
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js",
    "*.tsx": "${capture}.js",
    "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml",
    "tailwind.config.*": "tailwind.config.*",
    "tsconfig.json": "tsconfig.*.json",
    ".eslintrc.*": ".eslintignore,.eslintrc.*",
    ".prettierrc.*": ".prettierignore,.prettierrc.*"
  },

  // Search
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.expo": true,
    "**/coverage": true,
    "**/.git": true
  },

  // Tailwind CSS IntelliSense
  "tailwindCSS.experimental.classRegex": [
    ["className\\s*=\\s*[\"'`]([^\"'`]*)[\"'`]", "([^\"'`]*)"],
    ["className\\s*=\\s*{[\"'`]([^\"'`]*)[\"'`]}", "([^\"'`]*)"]
  ]
}

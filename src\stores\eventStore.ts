import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { databaseService } from '../database';
import {
  CreateEventData,
  Event,
  EventType,
  EventWithCountdown,
  UpdateEventData,
} from '../types/event.types';

interface EventState {
  events: Event[];
  upcomingEvents: EventWithCountdown[];
  isLoading: boolean;
  error: string | null;
}

interface EventActions {
  // Event CRUD operations
  createEvent: (data: CreateEventData) => Promise<Event | null>;
  updateEvent: (id: string, data: UpdateEventData) => Promise<Event | null>;
  deleteEvent: (id: string) => Promise<boolean>;

  // Fetch operations
  fetchEvents: (userId: string) => Promise<void>;
  fetchUpcomingEvents: (userId: string, limit?: number) => Promise<void>;

  // Utility functions
  getEventsByType: (type: EventType) => Event[];
  getEventsInDateRange: (startDate: Date, endDate: Date) => Event[];
  clearEvents: () => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
}

type EventStore = EventState & EventActions;

export const useEventStore = create<EventStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        events: [],
        upcomingEvents: [],
        isLoading: false,
        error: null,

        // Actions
        createEvent: async (data: CreateEventData) => {
          try {
            set({ isLoading: true, error: null });

            // Get current user ID (assuming it's available in the app)
            const userId = 'default-user'; // This should come from user context/store

            const newEvent = await databaseService.eventRepository.create(userId, data);

            set(state => ({
              events: [...state.events, newEvent],
              isLoading: false,
            }));

            // Refresh upcoming events
            await get().fetchUpcomingEvents(userId);

            return newEvent;
          } catch (error) {
            // Failed to create event
            set({
              error: error instanceof Error ? error.message : 'Failed to create event',
              isLoading: false,
            });
            return null;
          }
        },

        updateEvent: async (id: string, data: UpdateEventData) => {
          try {
            set({ isLoading: true, error: null });

            const updatedEvent = await databaseService.eventRepository.update(id, data);

            if (updatedEvent) {
              set(state => ({
                events: state.events.map(event => (event.id === id ? updatedEvent : event)),
                isLoading: false,
              }));

              // Refresh upcoming events
              const userId = 'default-user'; // This should come from user context/store
              await get().fetchUpcomingEvents(userId);
            }

            return updatedEvent;
          } catch (error) {
            // Failed to update event
            set({
              error: error instanceof Error ? error.message : 'Failed to update event',
              isLoading: false,
            });
            return null;
          }
        },

        deleteEvent: async (id: string) => {
          try {
            set({ isLoading: true, error: null });

            const success = await databaseService.eventRepository.delete(id);

            if (success) {
              set(state => ({
                events: state.events.filter(event => event.id !== id),
                upcomingEvents: state.upcomingEvents.filter(event => event.id !== id),
                isLoading: false,
              }));
            }

            return success;
          } catch (error) {
            // Failed to delete event
            set({
              error: error instanceof Error ? error.message : 'Failed to delete event',
              isLoading: false,
            });
            return false;
          }
        },

        fetchEvents: async (userId: string) => {
          try {
            set({ isLoading: true, error: null });

            const events = await databaseService.eventRepository.findByUserId(userId);

            set({
              events,
              isLoading: false,
            });
          } catch (error) {
            // Failed to fetch events
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch events',
              isLoading: false,
            });
          }
        },

        fetchUpcomingEvents: async (userId: string, limit?: number) => {
          try {
            set({ isLoading: true, error: null });

            const upcomingEvents = await databaseService.eventRepository.findUpcomingByUserId(
              userId,
              limit
            );

            set({
              upcomingEvents,
              isLoading: false,
            });
          } catch (error) {
            // Failed to fetch upcoming events
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch upcoming events',
              isLoading: false,
            });
          }
        },

        getEventsByType: (type: EventType) => {
          const { events } = get();
          return events.filter(event => event.type === type);
        },

        getEventsInDateRange: (startDate: Date, endDate: Date) => {
          const { events } = get();
          return events.filter(event => {
            const eventDate = new Date(event.date);
            return eventDate >= startDate && eventDate <= endDate;
          });
        },

        clearEvents: () => {
          set({
            events: [],
            upcomingEvents: [],
            error: null,
          });
        },

        setError: (error: string | null) => {
          set({ error });
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },
      }),
      {
        name: 'event-store',
        partialize: state => ({
          events: state.events,
          upcomingEvents: state.upcomingEvents,
        }),
      }
    ),
    {
      name: 'event-store',
    }
  )
);

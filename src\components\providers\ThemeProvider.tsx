import { StatusBar } from 'expo-status-bar';
import React, { createContext, ReactNode, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { useAppStore } from '../../stores/appStore';

interface ThemeContextType {
  theme: 'light' | 'dark' | 'system';
  resolvedTheme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme();
  const { theme, setTheme } = useAppStore();

  // Resolve the actual theme based on user preference and system setting
  const resolvedTheme = theme === 'system' ? (systemColorScheme ?? 'light') : theme;

  // Update the app store when system theme changes (for 'system' preference)
  useEffect(() => {
    if (theme === 'system' && systemColorScheme) {
      // The resolved theme will automatically update when systemColorScheme changes
      // No need to update the store as it should remain 'system'
    }
  }, [systemColorScheme, theme]);

  const contextValue: ThemeContextType = {
    theme,
    resolvedTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {/* Configure status bar based on resolved theme */}
      <StatusBar style={resolvedTheme === 'dark' ? 'light' : 'dark'} translucent={true} />
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook to get the resolved theme for styling
export function useResolvedTheme() {
  const { resolvedTheme } = useTheme();
  return resolvedTheme;
}

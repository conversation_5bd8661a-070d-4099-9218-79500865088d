import {
  Column,
  CreateDate<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('habit_streaks')
export class HabitStreak {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  habit_id: string;

  @Column({ type: 'date' })
  start_date: string; // Format: YYYY-MM-DD

  @Column({ type: 'date', nullable: true })
  end_date?: string; // Format: YYYY-MM-DD, null if streak is ongoing

  @Column({ type: 'integer', default: 1 })
  length: number; // Number of consecutive days

  @Column({ type: 'boolean', default: true })
  is_current: boolean; // Whether this is the current active streak

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne('Habit', 'streaks', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'habit_id' })
  habit: any;
}

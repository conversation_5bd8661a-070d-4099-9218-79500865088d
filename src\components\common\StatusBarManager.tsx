import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { useColorScheme } from 'react-native';

interface StatusBarManagerProps {
  backgroundColor?: string;
  style?: 'auto' | 'light' | 'dark';
  translucent?: boolean;
}

const StatusBarManager: React.FC<StatusBarManagerProps> = ({
  backgroundColor,
  style = 'auto',
  translucent = false,
}) => {
  const colorScheme = useColorScheme();

  // Determine the appropriate style based on theme and background
  const getStatusBarStyle = () => {
    if (style !== 'auto') return style;

    // If we have a custom background color, determine contrast
    if (backgroundColor) {
      // Simple heuristic: if background is dark, use light content
      const isDarkBackground =
        backgroundColor.toLowerCase().includes('dark') ||
        backgroundColor.includes('#000') ||
        backgroundColor.includes('#1') ||
        backgroundColor.includes('#2') ||
        backgroundColor.includes('#3');
      return isDarkBackground ? 'light' : 'dark';
    }

    // Default to theme-based
    return colorScheme === 'dark' ? 'light' : 'dark';
  };

  return (
    <StatusBar
      style={getStatusBarStyle()}
      backgroundColor={backgroundColor}
      translucent={translucent}
    />
  );
};

export default StatusBarManager;

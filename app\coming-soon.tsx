import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useResolvedTheme } from '../src/components';

interface FeatureInfo {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  features: string[];
  estimatedRelease?: string;
}

const featureMap: Record<string, FeatureInfo> = {
  analytics: {
    title: 'Analytics & Insights',
    description: 'Track your progress with detailed analytics and insights',
    icon: 'analytics-outline',
    features: [
      'Habit completion trends and patterns',
      'Weekly and monthly progress reports',
      'Streak analysis and achievements',
      'Productivity insights and recommendations',
      'Goal tracking and milestone celebrations',
      'Export data for external analysis',
    ],
    estimatedRelease: 'Q2 2024',
  },
  breathing: {
    title: 'Breathing Exercises',
    description: 'Guided breathing exercises and mindfulness practices',
    icon: 'leaf-outline',
    features: [
      'Guided breathing sessions (4-7-8, Box breathing)',
      'Customizable breathing patterns',
      'Mindfulness meditation timers',
      'Stress relief and relaxation techniques',
      'Progress tracking for mindfulness practice',
      'Integration with habit tracking',
    ],
    estimatedRelease: 'Q3 2024',
  },
  goals: {
    title: 'Goals & Milestones',
    description: 'Set and track long-term objectives and achievements',
    icon: 'trophy-outline',
    features: [
      'Long-term goal setting and tracking',
      'Milestone creation and celebration',
      'Goal breakdown into actionable habits',
      'Progress visualization and charts',
      'Achievement badges and rewards',
      'Goal sharing and accountability',
    ],
    estimatedRelease: 'Q4 2024',
  },
  backup: {
    title: 'Backup & Sync',
    description: 'Secure your data across multiple devices',
    icon: 'cloud-outline',
    features: [
      'Automatic cloud backup',
      'Cross-device synchronization',
      'Data export and import',
      'Account-based data storage',
      'Offline data protection',
      'Privacy-focused encryption',
    ],
    estimatedRelease: 'Q1 2025',
  },
  default: {
    title: 'Coming Soon',
    description: 'This feature is currently in development',
    icon: 'construct-outline',
    features: [
      'Enhanced user experience',
      'Improved functionality',
      'Better integration with existing features',
      'User-requested improvements',
    ],
  },
};

export default function ComingSoonScreen() {
  const { feature } = useLocalSearchParams<{ feature?: string }>();

  const resolvedTheme = useResolvedTheme();

  const featureInfo = featureMap[feature || 'default'] || featureMap.default;

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      <StatusBar style="auto" />

      {/* Header */}
      <View className="bg-white px-6 pb-6 pt-12 shadow-sm dark:bg-gray-800">
        <View className="flex-row items-center">
          <TouchableOpacity onPress={() => router.back()} className="mr-4">
            <Ionicons
              name="arrow-back"
              size={24}
              color={resolvedTheme === 'dark' ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
          <View className="flex-1">
            <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {featureInfo.title}
            </Text>
            <Text className="mt-1 text-gray-600 dark:text-gray-400">Coming Soon</Text>
          </View>
        </View>
      </View>

      {/* Content */}
      <View className="flex-1 px-6 py-8">
        {/* Feature Icon */}
        <View className="mb-8 items-center">
          <View className="mb-6 h-32 w-32 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
            <Ionicons
              name={featureInfo.icon}
              size={64}
              color={resolvedTheme === 'dark' ? '#3B82F6' : '#2563EB'}
            />
          </View>
          <Text className="mb-3 text-center text-2xl font-bold text-gray-900 dark:text-gray-100">
            {featureInfo.title}
          </Text>
          <Text className="text-center text-lg text-gray-600 dark:text-gray-400">
            {featureInfo.description}
          </Text>
          {featureInfo.estimatedRelease && (
            <View className="mt-4 rounded-full bg-blue-100 px-4 py-2 dark:bg-blue-900">
              <Text className="font-medium text-blue-600 dark:text-blue-400">
                Expected: {featureInfo.estimatedRelease}
              </Text>
            </View>
          )}
        </View>

        {/* Planned Features */}
        <View className="mb-6 rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
          <Text className="mb-4 text-lg font-semibold text-gray-900 dark:text-gray-100">
            Planned Features
          </Text>
          <View className="space-y-3">
            {featureInfo.features.map((feature, index) => (
              <View key={index} className="flex-row items-start">
                <View className="mr-3 mt-2 h-2 w-2 rounded-full bg-blue-500" />
                <Text className="flex-1 text-gray-600 dark:text-gray-400">{feature}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Development Status */}
        <View className="mb-6 rounded-xl bg-orange-50 p-6 shadow-sm dark:bg-orange-900">
          <View className="mb-3 flex-row items-center">
            <Ionicons name="hammer-outline" size={24} color="#F59E0B" />
            <Text className="ml-2 text-lg font-semibold text-orange-800 dark:text-orange-200">
              In Development
            </Text>
          </View>
          <Text className="text-orange-700 dark:text-orange-300">
            We&apos;re actively working on this feature. Your feedback and suggestions help us
            prioritize development and ensure we build exactly what you need.
          </Text>
        </View>

        {/* Action Buttons */}
        <View className="space-y-3">
          <TouchableOpacity onPress={() => router.back()} className="rounded-xl bg-blue-500 py-4">
            <Text className="text-center text-lg font-semibold text-white">Back to App</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              // TODO: Implement feedback/suggestion feature
              router.back();
            }}
            className="rounded-xl bg-gray-200 py-4 dark:bg-gray-700"
          >
            <Text className="text-center text-lg font-semibold text-gray-700 dark:text-gray-300">
              Send Feedback
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

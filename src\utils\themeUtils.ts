/**
 * Utility functions for theme-aware styling
 */

export interface ThemeColors {
  light: string;
  dark: string;
}

/**
 * Get the appropriate color based on the current theme
 */
export const getThemeColor = (colors: ThemeColors, isDark: boolean): string => {
  return isDark ? colors.dark : colors.light;
};

/**
 * Common theme-aware color definitions
 */
export const THEME_COLORS = {
  background: {
    light: '#FFFFFF',
    dark: '#1F2937',
  },
  surface: {
    light: '#F9FAFB',
    dark: '#111827',
  },
  text: {
    primary: {
      light: '#111827',
      dark: '#F9FAFB',
    },
    secondary: {
      light: '#6B7280',
      dark: '#9CA3AF',
    },
  },
  border: {
    light: '#E5E7EB',
    dark: '#374151',
  },
  accent: {
    light: '#2563EB',
    dark: '#3B82F6',
  },
  success: {
    light: '#059669',
    dark: '#10B981',
  },
  warning: {
    light: '#D97706',
    dark: '#F59E0B',
  },
  error: {
    light: '#DC2626',
    dark: '#EF4444',
  },
} as const;

/**
 * Get theme-aware Tailwind CSS classes
 */
export const getThemeClasses = (isDark: boolean) => ({
  background: isDark ? 'bg-gray-800' : 'bg-white',
  surface: isDark ? 'bg-gray-900' : 'bg-gray-50',
  text: {
    primary: isDark ? 'text-gray-100' : 'text-gray-900',
    secondary: isDark ? 'text-gray-400' : 'text-gray-600',
  },
  border: isDark ? 'border-gray-700' : 'border-gray-200',
  accent: isDark ? 'text-blue-400' : 'text-blue-600',
});

/**
 * Status bar style based on theme
 */
export const getStatusBarStyle = (isDark: boolean): 'light' | 'dark' => {
  return isDark ? 'light' : 'dark';
};

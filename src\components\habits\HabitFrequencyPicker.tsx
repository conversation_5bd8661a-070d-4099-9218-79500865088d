import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { HabitFrequency } from '../../types/habit.types';

interface HabitFrequencyPickerProps {
  selectedFrequency: HabitFrequency;
  onFrequencySelect: (frequency: HabitFrequency) => void;
  label?: string;
}

const FREQUENCY_CONFIG: Record<
  HabitFrequency,
  { label: string; description: string; icon: keyof typeof Ionicons.glyphMap }
> = {
  daily: {
    label: 'Daily',
    description: 'Every day',
    icon: 'calendar',
  },
  weekly: {
    label: 'Weekly',
    description: 'Once a week',
    icon: 'calendar-outline',
  },
  monthly: {
    label: 'Monthly',
    description: 'Once a month',
    icon: 'calendar-clear-outline',
  },
};

const HabitFrequencyPicker: React.FC<HabitFrequencyPickerProps> = ({
  selectedFrequency,
  onFrequencySelect,
  label = 'Frequency',
}) => {
  return (
    <View className="mb-4">
      <Text className="mb-3 text-base font-medium text-gray-900 dark:text-gray-100">{label}</Text>
      <View className="space-y-2">
        {Object.entries(FREQUENCY_CONFIG).map(([frequency, config]) => {
          const isSelected = selectedFrequency === frequency;
          return (
            <TouchableOpacity
              key={frequency}
              onPress={() => onFrequencySelect(frequency as HabitFrequency)}
              className={`flex-row items-center justify-between rounded-xl border-2 p-4 ${
                isSelected
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
              }`}
            >
              <View className="flex-1 flex-row items-center">
                <Ionicons
                  name={config.icon}
                  size={24}
                  color={isSelected ? '#3B82F6' : '#6B7280'}
                  style={{ marginRight: 12 }}
                />
                <View>
                  <Text
                    className={`text-base font-medium ${
                      isSelected
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-gray-900 dark:text-gray-100'
                    }`}
                  >
                    {config.label}
                  </Text>
                  <Text
                    className={`text-sm ${
                      isSelected
                        ? 'text-blue-500 dark:text-blue-300'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}
                  >
                    {config.description}
                  </Text>
                </View>
              </View>
              {isSelected && <Ionicons name="checkmark-circle" size={24} color="#3B82F6" />}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

export default HabitFrequencyPicker;

/**
 * Type definitions for event countdown data structures
 */

export type EventType = 'birthday' | 'anniversary' | 'holiday' | 'custom';

export interface Event {
  id: string;
  name: string;
  type: EventType;
  date: Date;
  description?: string;
  isRecurring: boolean;
  reminderDays?: number; // Days before event to show reminder
  color: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EventWithCountdown extends Event {
  daysRemaining: number;
  isToday: boolean;
  isPast: boolean;
  nextOccurrence?: Date; // For recurring events
}

export interface CreateEventData {
  name: string;
  type: EventType;
  date: Date;
  description?: string;
  isRecurring: boolean;
  reminderDays?: number;
  color: string;
}

export interface UpdateEventData extends Partial<CreateEventData> {
  id: string;
}

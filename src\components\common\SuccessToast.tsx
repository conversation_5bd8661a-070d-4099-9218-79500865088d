import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useRef } from 'react';
import { Animated, Text, TouchableOpacity } from 'react-native';

interface SuccessToastProps {
  visible: boolean;
  message: string;
  onHide: () => void;
  duration?: number;
  type?: 'success' | 'error' | 'warning' | 'info';
}

const SuccessToast: React.FC<SuccessToastProps> = ({
  visible,
  message,
  onHide,
  duration = 3000,
  type = 'success',
}) => {
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  const hideToast = useCallback(() => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onHide();
    });
  }, [slideAnim, opacityAnim, onHide]);

  useEffect(() => {
    if (visible) {
      // Slide in and fade in
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto hide after duration
      const timer = setTimeout(() => {
        hideToast();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible, duration, hideToast, opacityAnim, slideAnim]);

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          backgroundColor: 'bg-green-500',
          icon: 'checkmark-circle' as const,
          iconColor: '#FFFFFF',
        };
      case 'error':
        return {
          backgroundColor: 'bg-red-500',
          icon: 'close-circle' as const,
          iconColor: '#FFFFFF',
        };
      case 'warning':
        return {
          backgroundColor: 'bg-yellow-500',
          icon: 'warning' as const,
          iconColor: '#FFFFFF',
        };
      case 'info':
        return {
          backgroundColor: 'bg-blue-500',
          icon: 'information-circle' as const,
          iconColor: '#FFFFFF',
        };
      default:
        return {
          backgroundColor: 'bg-green-500',
          icon: 'checkmark-circle' as const,
          iconColor: '#FFFFFF',
        };
    }
  };

  const config = getToastConfig();

  if (!visible) return null;

  return (
    <Animated.View
      style={{
        transform: [{ translateY: slideAnim }],
        opacity: opacityAnim,
      }}
      className="absolute left-4 right-4 top-12 z-50"
    >
      <TouchableOpacity
        onPress={hideToast}
        activeOpacity={0.9}
        className={`${config.backgroundColor} flex-row items-center rounded-xl p-4 shadow-lg`}
      >
        <Ionicons name={config.icon} size={24} color={config.iconColor} />
        <Text className="ml-3 flex-1 font-medium text-white">{message}</Text>
        <Ionicons name="close" size={20} color={config.iconColor} style={{ opacity: 0.7 }} />
      </TouchableOpacity>
    </Animated.View>
  );
};

export default SuccessToast;

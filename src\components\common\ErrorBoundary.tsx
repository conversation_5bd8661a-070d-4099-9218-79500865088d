import { Ionicons } from '@expo/vector-icons';
import React, { Component, ReactNode } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // ErrorBoundary caught an error
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <View className="flex-1 items-center justify-center bg-gray-50 p-6 dark:bg-gray-900">
          <View className="items-center">
            <View className="mb-4 h-20 w-20 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">
              <Ionicons name="warning-outline" size={40} color="#EF4444" />
            </View>

            <Text className="mb-2 text-center text-xl font-bold text-gray-900 dark:text-gray-100">
              Something went wrong
            </Text>

            <Text className="mb-6 max-w-sm text-center text-gray-600 dark:text-gray-400">
              We encountered an unexpected error. Please try again or restart the app.
            </Text>

            {__DEV__ && this.state.error && (
              <View className="mb-6 max-w-sm rounded-lg bg-red-50 p-4 dark:bg-red-900">
                <Text className="font-mono text-xs text-red-800 dark:text-red-200">
                  {this.state.error.message}
                </Text>
              </View>
            )}

            <TouchableOpacity
              onPress={this.handleRetry}
              className="rounded-xl bg-blue-500 px-6 py-3"
            >
              <Text className="font-semibold text-white">Try Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

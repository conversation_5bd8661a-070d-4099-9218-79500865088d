import { Repository } from 'typeorm';
import { AppDataSource } from '../config';
import { HabitStreak } from '../entities';

export class HabitStreakRepository {
  private repository: Repository<HabitStreak>;

  constructor() {
    this.repository = AppDataSource.getRepository(HabitStreak);
  }

  async create(streakData: Partial<HabitStreak>): Promise<HabitStreak> {
    // Import UUID generator locally to avoid auto-formatting removal
    const { generateUUID } = await import('../../utils/uuidUtils');

    const streak = this.repository.create({
      ...streakData,
      id: generateUUID(), // Manually set UUID to avoid crypto.getRandomValues() issue
    });
    return await this.repository.save(streak);
  }

  async findById(id: string): Promise<HabitStreak | null> {
    return await this.repository.findOne({
      where: { id },
      relations: ['habit'],
    });
  }

  async findByHabitId(habitId: string): Promise<HabitStreak[]> {
    return await this.repository.find({
      where: { habit_id: habitId },
      order: { start_date: 'DESC' },
    });
  }

  async findCurrentStreak(habitId: string): Promise<HabitStreak | null> {
    return await this.repository.findOne({
      where: { habit_id: habitId, is_current: true },
    });
  }

  async update(id: string, streakData: Partial<HabitStreak>): Promise<HabitStreak | null> {
    await this.repository.update(id, streakData);
    return await this.findById(id);
  }

  async endCurrentStreak(habitId: string, endDate: string): Promise<void> {
    await this.repository.update(
      { habit_id: habitId, is_current: true },
      { is_current: false, end_date: endDate }
    );
  }

  async startNewStreak(habitId: string, startDate: string): Promise<HabitStreak> {
    // End any existing current streak
    await this.endCurrentStreak(habitId, startDate);

    // Create new streak
    return await this.create({
      habit_id: habitId,
      start_date: startDate,
      length: 1,
      is_current: true,
    });
  }

  async updateStreakLength(habitId: string, newLength: number): Promise<void> {
    await this.repository.update({ habit_id: habitId, is_current: true }, { length: newLength });
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== 0;
  }

  async getLongestStreak(habitId: string): Promise<number> {
    const streaks = await this.findByHabitId(habitId);
    return streaks.reduce((max, streak) => Math.max(max, streak.length), 0);
  }

  async getCurrentStreakLength(habitId: string): Promise<number> {
    const currentStreak = await this.findCurrentStreak(habitId);
    return currentStreak?.length || 0;
  }
}

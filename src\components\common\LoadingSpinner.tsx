import React from 'react';
import { ActivityIndicator, Text, View } from 'react-native';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  message?: string;
  fullScreen?: boolean;
  overlay?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color,
  message,
  fullScreen = false,
  overlay = false,
}) => {
  const defaultColor = color || '#3B82F6';

  const content = (
    <View className={`items-center justify-center ${fullScreen ? 'flex-1' : 'py-8'}`}>
      <ActivityIndicator size={size} color={defaultColor} />
      {message && (
        <Text className="mt-3 text-center text-gray-600 dark:text-gray-400">{message}</Text>
      )}
    </View>
  );

  if (overlay) {
    return (
      <View className="absolute inset-0 z-50 items-center justify-center bg-black/50">
        <View className="mx-6 rounded-xl bg-white p-6 shadow-lg dark:bg-gray-800">{content}</View>
      </View>
    );
  }

  return content;
};

export default LoadingSpinner;

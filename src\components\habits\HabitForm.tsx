import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { HabitFormData, HabitUnit } from '../../types/habit.types';
import HabitCategoryPicker from './HabitCategoryPicker';
import HabitColorPicker from './HabitColorPicker';
import HabitFrequencyPicker from './HabitFrequencyPicker';
import HabitSubTaskManager from './HabitSubTaskManager';
import HabitTimeRangePicker from './HabitTimeRangePicker';

interface HabitFormProps {
  initialData?: Partial<HabitFormData>;
  onSubmit: (data: HabitFormData) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
  isLoading?: boolean;
}

const UNIT_OPTIONS: { value: HabitUnit; label: string }[] = [
  { value: 'times', label: 'times' },
  { value: 'minutes', label: 'minutes' },
  { value: 'hours', label: 'hours' },
  { value: 'pages', label: 'pages' },
  { value: 'glasses', label: 'glasses' },
  { value: 'steps', label: 'steps' },
  { value: 'kilometers', label: 'km' },
  { value: 'miles', label: 'miles' },
  { value: 'pounds', label: 'lbs' },
  { value: 'kilograms', label: 'kg' },
  { value: 'other', label: 'other' },
];

const HabitForm: React.FC<HabitFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isEditing = false,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<HabitFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    category: initialData?.category || 'other',
    frequency: initialData?.frequency || 'daily',
    target_count: initialData?.target_count || 1,
    unit: initialData?.unit || 'times',
    color: initialData?.color || '#3B82F6',
    reminder_time: initialData?.reminder_time || '',
    reminder_enabled: initialData?.reminder_enabled || false,
    // New hierarchical fields
    purpose: initialData?.purpose || '',
    timeRange: initialData?.timeRange,
    subTasks: initialData?.subTasks || [],
  });

  const [errors, setErrors] = useState<Partial<Record<keyof HabitFormData, string>>>({});
  const [showUnitPicker, setShowUnitPicker] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof HabitFormData, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Habit name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Habit name must be at least 2 characters';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Habit name must be less than 100 characters';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    if (!formData.purpose.trim()) {
      newErrors.purpose = 'Purpose is required - explain why this habit matters to you';
    } else if (formData.purpose.trim().length < 10) {
      newErrors.purpose = 'Purpose should be at least 10 characters to be meaningful';
    }

    if (formData.target_count < 1) {
      newErrors.target_count = 'Target count must be at least 1';
    } else if (formData.target_count > 999) {
      newErrors.target_count = 'Target count must be less than 1000';
    }

    // Validate time range if provided
    if (formData.timeRange) {
      const { startTime, endTime } = formData.timeRange;
      const start = new Date(`2000-01-01T${startTime}:00`);
      const end = new Date(`2000-01-01T${endTime}:00`);

      if (start >= end) {
        newErrors.timeRange = 'End time must be after start time';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to save habit');
    }
  };

  const updateFormData = <K extends keyof HabitFormData>(key: K, value: HabitFormData[K]) => {
    setFormData(prev => ({ ...prev, [key]: value }));
    // Clear error for this field when user starts typing
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: undefined }));
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
    >
      <ScrollView className="flex-1 bg-gray-50 dark:bg-gray-900">
        <View className="p-6">
          {/* Header */}
          <View className="mb-6 flex-row items-center justify-between">
            <TouchableOpacity onPress={onCancel}>
              <Ionicons name="close" size={24} color="#6B7280" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-gray-900 dark:text-gray-100">
              {isEditing ? 'Edit Habit' : 'New Habit'}
            </Text>
            <TouchableOpacity
              onPress={handleSubmit}
              disabled={isLoading}
              className={`rounded-lg px-4 py-2 ${
                isLoading ? 'bg-gray-300 dark:bg-gray-700' : 'bg-blue-500'
              }`}
            >
              <Text className="font-medium text-white">{isLoading ? 'Saving...' : 'Save'}</Text>
            </TouchableOpacity>
          </View>

          {/* Form Fields */}
          <View className="space-y-6">
            {/* Name Field */}
            <View>
              <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
                Habit Name *
              </Text>
              <TextInput
                value={formData.name}
                onChangeText={text => updateFormData('name', text)}
                placeholder="e.g., Drink 8 glasses of water"
                placeholderTextColor="#9CA3AF"
                className={`rounded-xl border-2 bg-white px-4 py-3 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ${
                  errors.name ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'
                }`}
                maxLength={100}
              />
              {errors.name && <Text className="mt-1 text-sm text-red-500">{errors.name}</Text>}
            </View>

            {/* Purpose Field */}
            <View>
              <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
                Purpose *
              </Text>
              <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
                Why do you want to build this habit? What will it help you achieve?
              </Text>
              <TextInput
                value={formData.purpose}
                onChangeText={text => updateFormData('purpose', text)}
                placeholder="e.g., To improve my health and energy levels, to become more disciplined..."
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={3}
                className={`rounded-xl border-2 bg-white px-4 py-3 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ${
                  errors.purpose ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'
                }`}
                style={{ textAlignVertical: 'top' }}
                maxLength={300}
              />
              {errors.purpose && (
                <Text className="mt-1 text-sm text-red-500">{errors.purpose}</Text>
              )}
            </View>

            {/* Description Field */}
            <View>
              <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
                Description (Optional)
              </Text>
              <TextInput
                value={formData.description}
                onChangeText={text => updateFormData('description', text)}
                placeholder="Add more details about your habit..."
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={3}
                className={`rounded-xl border-2 bg-white px-4 py-3 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ${
                  errors.description ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'
                }`}
                style={{ textAlignVertical: 'top' }}
                maxLength={500}
              />
              {errors.description && (
                <Text className="mt-1 text-sm text-red-500">{errors.description}</Text>
              )}
            </View>

            {/* Category Picker */}
            <HabitCategoryPicker
              selectedCategory={formData.category}
              onCategorySelect={category => updateFormData('category', category)}
            />

            {/* Frequency Picker */}
            <HabitFrequencyPicker
              selectedFrequency={formData.frequency}
              onFrequencySelect={frequency => updateFormData('frequency', frequency)}
            />

            {/* Target Count and Unit */}
            <View>
              <Text className="mb-3 text-base font-medium text-gray-900 dark:text-gray-100">
                Target
              </Text>
              <View className="flex-row space-x-3">
                <View className="flex-1">
                  <TextInput
                    value={formData.target_count.toString()}
                    onChangeText={text => {
                      const num = parseInt(text) || 1;
                      updateFormData('target_count', Math.max(1, Math.min(999, num)));
                    }}
                    keyboardType="numeric"
                    className={`rounded-xl border-2 bg-white px-4 py-3 text-center text-gray-900 dark:bg-gray-800 dark:text-gray-100 ${
                      errors.target_count
                        ? 'border-red-500'
                        : 'border-gray-200 dark:border-gray-700'
                    }`}
                  />
                </View>
                <TouchableOpacity
                  onPress={() => setShowUnitPicker(!showUnitPicker)}
                  className="flex-2 flex-row items-center justify-between rounded-xl border-2 border-gray-200 bg-white px-4 py-3 dark:border-gray-700 dark:bg-gray-800"
                >
                  <Text className="text-gray-900 dark:text-gray-100">
                    {UNIT_OPTIONS.find(u => u.value === formData.unit)?.label || 'times'}
                  </Text>
                  <Ionicons
                    name={showUnitPicker ? 'chevron-up' : 'chevron-down'}
                    size={20}
                    color="#6B7280"
                  />
                </TouchableOpacity>
              </View>
              {errors.target_count && (
                <Text className="mt-1 text-sm text-red-500">{errors.target_count}</Text>
              )}
            </View>

            {/* Unit Picker Dropdown */}
            {showUnitPicker && (
              <View className="overflow-hidden rounded-xl border-2 border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
                {UNIT_OPTIONS.map(unit => (
                  <TouchableOpacity
                    key={unit.value}
                    onPress={() => {
                      updateFormData('unit', unit.value);
                      setShowUnitPicker(false);
                    }}
                    className={`border-b border-gray-100 px-4 py-3 dark:border-gray-700 ${
                      formData.unit === unit.value ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    <Text
                      className={`${
                        formData.unit === unit.value
                          ? 'font-medium text-blue-600 dark:text-blue-400'
                          : 'text-gray-900 dark:text-gray-100'
                      }`}
                    >
                      {unit.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Time Range Picker */}
            <HabitTimeRangePicker
              timeRange={formData.timeRange}
              onTimeRangeChange={timeRange => updateFormData('timeRange', timeRange)}
            />

            {/* Sub-task Manager */}
            <HabitSubTaskManager
              subTasks={formData.subTasks}
              onSubTasksChange={subTasks => updateFormData('subTasks', subTasks)}
            />

            {/* Color Picker */}
            <HabitColorPicker
              selectedColor={formData.color}
              onColorSelect={color => updateFormData('color', color)}
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default HabitForm;

import { Repository } from 'typeorm';
import { CreateEventData, EventWithCountdown, UpdateEventData } from '../../types/event.types';
import { AppDataSource } from '../config';
import { Event } from '../entities';

export class EventRepository {
  private repository: Repository<Event>;

  constructor() {
    this.repository = AppDataSource.getRepository(Event);
  }

  async create(userId: string, data: CreateEventData): Promise<Event> {
    const event = this.repository.create({
      ...data,
      userId,
    });
    return await this.repository.save(event);
  }

  async findById(id: string): Promise<Event | null> {
    return await this.repository.findOne({
      where: { id },
      relations: ['user'],
    });
  }

  async findByUserId(userId: string): Promise<Event[]> {
    return await this.repository.find({
      where: { userId },
      order: { date: 'ASC' },
    });
  }

  async findUpcomingByUserId(userId: string, limit?: number): Promise<EventWithCountdown[]> {
    const events = await this.repository.find({
      where: { userId },
      order: { date: 'ASC' },
      take: limit,
    });

    const now = new Date();
    const currentYear = now.getFullYear();

    return events
      .map(event => {
        const eventDate = new Date(event.date);
        const targetDate = new Date(eventDate);

        // For recurring events, find the next occurrence
        if (event.isRecurring) {
          targetDate.setFullYear(currentYear);

          // If the date has passed this year, move to next year
          if (targetDate < now) {
            targetDate.setFullYear(currentYear + 1);
          }
        }

        const timeDiff = targetDate.getTime() - now.getTime();
        const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));

        return {
          ...event,
          daysRemaining: Math.max(0, daysRemaining),
          isToday: daysRemaining === 0,
          isPast: daysRemaining < 0 && !event.isRecurring,
          nextOccurrence: event.isRecurring ? targetDate : undefined,
        };
      })
      .filter(event => !event.isPast || event.isRecurring);
  }

  async update(id: string, data: UpdateEventData): Promise<Event | null> {
    await this.repository.update(id, data);
    return await this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  async deleteByUserId(userId: string): Promise<void> {
    await this.repository.delete({ userId });
  }

  async findEventsByType(userId: string, type: string): Promise<Event[]> {
    return await this.repository.find({
      where: { userId, type: type as any },
      order: { date: 'ASC' },
    });
  }

  async findEventsInDateRange(userId: string, startDate: Date, endDate: Date): Promise<Event[]> {
    return await this.repository
      .createQueryBuilder('event')
      .where('event.userId = :userId', { userId })
      .andWhere('event.date BETWEEN :startDate AND :endDate', {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })
      .orderBy('event.date', 'ASC')
      .getMany();
  }
}

# WinArc: Habit Tracker - Project Structure

This document outlines the organized folder structure and architecture of the
WinArc habit tracking app built with **Expo-managed React Native**.

## 📁 Project Structure

```
WinArc/
├── app/                          # Expo Router app directory
│   ├── (tabs)/                   # Tab navigation group
│   │   ├── _layout.tsx          # Tab layout configuration
│   │   ├── index.tsx            # Home screen
│   │   ├── habits.tsx           # Habits screen
│   │   ├── more.tsx             # More tab with quick access dropdown
│   │   ├── pomodoro.tsx         # Pomodoro timer screen
│   │   └── profile.tsx          # Profile screen
│   ├── _layout.tsx              # Root layout
│   ├── index.tsx                # App entry point
│   ├── onboarding.tsx           # Onboarding screen
│   ├── habit-detail.tsx         # Habit detail screen (future)
│   └── habit-form.tsx           # Habit form screen (future)
├── src/                         # Source code directory
│   ├── components/              # Reusable UI components
│   │   ├── ui/                  # Generic UI components
│   │   ├── habits/              # Habit-specific components
│   │   │   ├── HabitForm.tsx    # Habit creation/editing form
│   │   │   ├── HabitCard.tsx    # Habit display card
│   │   │   ├── HabitSubTaskManager.tsx # Sub-task management
│   │   │   ├── HabitTimeRangePicker.tsx # Time range selection
│   │   │   └── index.ts         # Habit component exports
│   │   ├── timer/               # Timer-specific components
│   │   ├── events/              # Event countdown components
│   │   └── index.ts             # Component exports
│   ├── database/                # Database layer
│   │   ├── entities/            # TypeORM entities
│   │   │   ├── User.ts
│   │   │   ├── Habit.ts
│   │   │   ├── HabitCompletion.ts
│   │   │   ├── HabitStreak.ts
│   │   │   ├── HabitSubTaskCompletion.ts
│   │   │   └── Event.ts
│   │   ├── repositories/        # Data access layer
│   │   │   ├── UserRepository.ts
│   │   │   ├── HabitRepository.ts
│   │   │   ├── HabitCompletionRepository.ts
│   │   │   ├── HabitStreakRepository.ts
│   │   │   └── EventRepository.ts
│   │   ├── DatabaseService.ts   # Database service singleton
│   │   └── index.ts             # Database exports
│   ├── stores/                  # Zustand state management
│   │   ├── userStore.ts         # User state
│   │   ├── habitStore.ts        # Habit management state
│   │   ├── appStore.ts          # Global app state
│   │   ├── timerStore.ts        # Pomodoro timer state
│   │   ├── eventStore.ts        # Event countdown state
│   │   └── index.ts             # Store exports
│   ├── services/                # Business logic services
│   │   ├── ThemeService.ts      # Theme management service
│   │   └── index.ts             # Service exports
│   ├── styles/                  # Styling and theme files
│   │   └── global.css           # Global Tailwind CSS styles
│   ├── hooks/                   # Custom React hooks
│   │   ├── useHabits.ts         # Habit management hook
│   │   ├── useTimer.ts          # Timer management hook
│   │   └── index.ts             # Hook exports
│   ├── utils/                   # Utility functions
│   │   ├── dateUtils.ts         # Date manipulation utilities
│   │   ├── formatUtils.ts       # Formatting utilities
│   │   ├── validationUtils.ts   # Validation utilities
│   │   ├── constants.ts         # App constants
│   │   └── index.ts             # Utility exports
│   └── types/                   # TypeScript type definitions
│       ├── habit.types.ts       # Habit-related types (with hierarchical structure)
│       ├── user.types.ts        # User-related types
│       ├── timer.types.ts       # Pomodoro timer types
│       ├── event.types.ts       # Event countdown types
│       ├── app.types.ts         # General app types
│       ├── database.types.ts    # Database-related types
│       └── index.ts             # Type exports
├── .vscode/                     # VS Code configuration
│   ├── settings.json            # Editor settings
│   └── extensions.json          # Recommended extensions
├── docs/                        # Project documentation
│   ├── README.md               # Documentation index
│   ├── FEATURES.md             # Feature specifications
│   ├── PROJECT_STRUCTURE.md    # This file
│   ├── ROADMAP.md              # Development roadmap
│   └── TECH_STACK.md           # Technology stack details
├── tailwind.config.js           # Tailwind CSS configuration
├── metro.config.js              # Metro bundler configuration
├── babel.config.js              # Babel configuration
├── eslint.config.js             # ESLint configuration
├── .prettierrc.js               # Prettier configuration
├── .prettierignore              # Prettier ignore rules
├── nativewind-env.d.ts          # NativeWind TypeScript declarations
└── package.json                 # Project dependencies and scripts
```

## 🏗️ Architecture Overview

### **App Layer** (`app/`)

- **Expo Router**: File-based routing system (built into Expo SDK)
- **Tab Navigation**: Bottom tab navigation with 4 main screens using (tabs)
  group
- **Screen Components**: Individual screen implementations as .tsx files
- **Layouts**: Navigation and layout configurations with \_layout.tsx files

### **Components Layer** (`src/components/`)

- **UI Components**: Reusable, generic UI elements (buttons, inputs, modals)
- **Feature Components**: Domain-specific components (habit cards, timer
  displays)
- **Composition**: Components are composed to build screens and features

### **Data Layer** (`src/database/`)

- **Entities**: TypeORM entity definitions for database tables
- **Repositories**: Data access layer with CRUD operations
- **Service**: Singleton database service managing connections and operations
- **Expo SQLite**: Local database using expo-sqlite module (built into Expo SDK)

### **State Management** (`src/stores/`)

- **Zustand**: Lightweight state management library
- **Store Separation**: Domain-specific stores (user, habits, app, timer)
- **Persistence**: Automatic state persistence for user preferences
- **DevTools**: Development debugging support

### **Business Logic** (`src/hooks/`)

- **Custom Hooks**: Encapsulate complex logic and state interactions
- **Reusability**: Share logic across multiple components
- **Separation of Concerns**: Keep components focused on UI

### **Utilities** (`src/utils/`)

- **Pure Functions**: Stateless utility functions
- **Date Handling**: Date manipulation and formatting
- **Validation**: Input validation and data sanitization
- **Constants**: App-wide constants and configuration

### **Type Safety** (`src/types/`)

- **TypeScript**: Full type coverage for better development experience
- **Domain Types**: Specific types for each feature domain
- **API Types**: Request/response type definitions
- **Utility Types**: Generic and helper types

## 🔧 Development Tools

### **Code Quality**

- **ESLint**: Code linting with Expo-compatible rules
- **Prettier**: Code formatting with Tailwind CSS support
- **TypeScript**: Static type checking
- **VS Code**: Optimized editor configuration

### **Styling**

- **NativeWind v4**: Tailwind CSS for React Native
- **Utility-First**: Consistent design system
- **Responsive**: Mobile-first responsive design
- **Theming**: Light/dark theme support

### **Scripts**

- `npm run lint`: Run ESLint
- `npm run lint:fix`: Fix ESLint issues
- `npm run format`: Format code with Prettier
- `npm run format:check`: Check code formatting
- `npm run type-check`: Run TypeScript checks
- `npm run dev:tools`: Run all development tools

## 📱 Features Implemented

### **Phase 1 - Technical Foundation**

- ✅ NativeWind v4 configuration
- ✅ SQLite + TypeORM database setup
- ✅ Zustand state management
- ✅ Expo Router navigation structure
- ✅ Development tools (ESLint, Prettier)
- ✅ Organized project structure

### **Core Functionality**

- ✅ User management and profiles
- ✅ Hierarchical habit creation with sub-tasks and purpose fields
- ✅ Habit tracking with completion history
- ✅ Completion tracking with streaks
- ✅ Pomodoro timer with work/break phases
- ✅ Event countdown system for important dates
- ✅ Light/dark theme support
- ✅ App settings and preferences
- ✅ Onboarding flow
- ✅ Android compatibility with TypeORM

## 🚀 Next Steps

### **Phase 2 - Enhanced Features**

- ✅ Hierarchical habit structure with sub-tasks
- ✅ Event countdown system
- ✅ Android compatibility fixes
- [ ] Complete time range picker with DateTimePicker
- [ ] Enhanced habit analytics and insights
- [ ] Smart notifications and reminders
- [ ] Data export/import functionality

### **Phase 3 - Advanced Features**

- [ ] Breathing exercises and mindfulness tools
- [ ] Advanced habit analytics with charts
- [ ] Notification system with smart reminders
- [ ] Social features and accountability
- [ ] Habit templates and sharing

### **Phase 4 - Polish**

- Animations and micro-interactions
- Performance optimizations
- Accessibility improvements
- Testing implementation

## 📚 Key Technologies

- **Expo-managed React Native**: Mobile app framework with Expo SDK
- **Expo SDK ~53.0**: Development platform and comprehensive module library
- **TypeScript**: Static type checking with full project coverage
- **NativeWind v4**: Tailwind CSS for React Native with compile-time
  optimizations
- **Zustand**: Lightweight state management
- **TypeORM**: Object-relational mapping for database operations
- **Expo SQLite**: Local database through expo-sqlite module
- **Expo Router**: File-based navigation system built into Expo

This structure provides a solid foundation for building a scalable, maintainable
habit tracking application with modern development practices and tools.

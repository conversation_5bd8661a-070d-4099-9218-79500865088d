import * as SQLite from 'expo-sqlite';
import { DataSource } from 'typeorm';
import {
  Event,
  Habit,
  HabitCompletion,
  HabitStreak,
  HabitSubTaskCompletion,
  User,
} from './entities';

// Create the database connection
export const AppDataSource = new DataSource({
  type: 'expo',
  database: 'winarc.db',
  driver: SQLite,
  entities: [User, Habit, HabitCompletion, HabitStreak, HabitSubTaskCompletion, Event],
  synchronize: true, // Set to false in production
  logging: __DEV__ ? ['error', 'warn'] : false, // Only show errors and warnings in development
});

// Initialize the database
export const initializeDatabase = async (): Promise<DataSource> => {
  try {
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      // Database initialized successfully
    }
    return AppDataSource;
  } catch (error) {
    // Error initializing database
    throw error;
  }
};

// Close the database connection
export const closeDatabase = async (): Promise<void> => {
  try {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      // Database connection closed
    }
  } catch (error) {
    // Error closing database
    throw error;
  }
};

/**
 * HabitCard Component Tests
 * Unit tests for the HabitCard component functionality
 */

import { fireEvent } from '@testing-library/react-native';
import React from 'react';
import { createMockHabit, createMockHabitCompletion, render } from '../../../test/utils';
import { HabitCard } from '../HabitCard';

describe('HabitCard', () => {
  const mockHabit = createMockHabit();
  const mockOnToggleCompletion = jest.fn();
  const mockOnPress = jest.fn();
  const mockOnLongPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render habit name and description', () => {
      const { getByText } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      expect(getByText(mockHabit.name)).toBeTruthy();
      expect(getByText(mockHabit.description)).toBeTruthy();
    });

    it('should display habit category and frequency', () => {
      const { getByText } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      expect(getByText(new RegExp(mockHabit.category, 'i'))).toBeTruthy();
      expect(getByText(new RegExp(mockHabit.frequency, 'i'))).toBeTruthy();
    });

    it('should show habit color indicator', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      const colorIndicator = getByTestId('habit-color-indicator');
      expect(colorIndicator).toBeTruthy();
    });

    it('should render in compact mode when specified', () => {
      const { queryByText } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
          compact={true}
        />
      );

      // In compact mode, description should not be visible
      expect(queryByText(mockHabit.description)).toBeFalsy();
    });
  });

  describe('Completion Status', () => {
    it('should show completed status when habit is completed today', () => {
      const completedHabit = createMockHabit({
        completions: [
          createMockHabitCompletion({
            completed_date: new Date().toISOString().split('T')[0],
          }),
        ],
      });

      const { getByTestId } = render(
        <HabitCard
          habit={completedHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      const completionButton = getByTestId('completion-button');
      expect(completionButton).toBeTruthy();
      // Check if the button shows completed state (you may need to adjust based on implementation)
    });

    it('should show incomplete status when habit is not completed today', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      const completionButton = getByTestId('completion-button');
      expect(completionButton).toBeTruthy();
    });
  });

  describe('Interactions', () => {
    it('should call onToggleCompletion when completion button is pressed', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      const completionButton = getByTestId('completion-button');
      fireEvent.press(completionButton);

      expect(mockOnToggleCompletion).toHaveBeenCalledWith(mockHabit);
    });

    it('should call onPress when card is pressed', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      const card = getByTestId('habit-card');
      fireEvent.press(card);

      expect(mockOnPress).toHaveBeenCalledWith(mockHabit);
    });

    it('should call onLongPress when card is long pressed', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
          onLongPress={mockOnLongPress}
        />
      );

      const card = getByTestId('habit-card');
      fireEvent(card, 'onLongPress');

      expect(mockOnLongPress).toHaveBeenCalledWith(mockHabit);
    });
  });

  describe('Selection Mode', () => {
    const mockOnToggleSelection = jest.fn();

    beforeEach(() => {
      mockOnToggleSelection.mockClear();
    });

    it('should show selection checkbox when in selection mode', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
          selectionMode={true}
          onToggleSelection={mockOnToggleSelection}
        />
      );

      const checkbox = getByTestId('selection-checkbox');
      expect(checkbox).toBeTruthy();
    });

    it('should not show selection checkbox when not in selection mode', () => {
      const { queryByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
          selectionMode={false}
        />
      );

      const checkbox = queryByTestId('selection-checkbox');
      expect(checkbox).toBeFalsy();
    });

    it('should call onToggleSelection when card is pressed in selection mode', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
          selectionMode={true}
          onToggleSelection={mockOnToggleSelection}
        />
      );

      const card = getByTestId('habit-card');
      fireEvent.press(card);

      expect(mockOnToggleSelection).toHaveBeenCalledWith(mockHabit);
      expect(mockOnPress).not.toHaveBeenCalled();
    });

    it('should show selected state styling when habit is selected', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
          selectionMode={true}
          isSelected={true}
          onToggleSelection={mockOnToggleSelection}
        />
      );

      const card = getByTestId('habit-card');
      expect(card).toBeTruthy();
      // You may want to check for specific styling classes or props
    });
  });

  describe('Progress Display', () => {
    it('should show progress when showProgress is true', () => {
      const { queryByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
          showProgress={true}
        />
      );

      // Check if progress indicator is shown (adjust based on implementation)
      queryByTestId('progress-indicator');
      // This test depends on your implementation
    });

    it('should not show progress when showProgress is false', () => {
      const { queryByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
          showProgress={false}
        />
      );

      const progressIndicator = queryByTestId('progress-indicator');
      expect(progressIndicator).toBeFalsy();
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility labels', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      const card = getByTestId('habit-card');
      expect(card).toHaveAccessibilityRole('button');
      expect(card).toHaveAccessibilityLabel(expect.stringContaining(mockHabit.name));
    });

    it('should have proper accessibility state for completion button', () => {
      const { getByTestId } = render(
        <HabitCard
          habit={mockHabit}
          onToggleCompletion={mockOnToggleCompletion}
          onPress={mockOnPress}
        />
      );

      const completionButton = getByTestId('completion-button');
      expect(completionButton).toHaveAccessibilityRole('button');
      expect(completionButton).toHaveAccessibilityLabel();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing habit data gracefully', () => {
      const incompleteHabit = createMockHabit({
        name: '',
        description: '',
      });

      expect(() => {
        render(
          <HabitCard
            habit={incompleteHabit}
            onToggleCompletion={mockOnToggleCompletion}
            onPress={mockOnPress}
          />
        );
      }).not.toThrow();
    });

    it('should handle missing callback functions gracefully', () => {
      expect(() => {
        render(
          <HabitCard
            habit={mockHabit}
            onToggleCompletion={mockOnToggleCompletion}
            // onPress is optional
          />
        );
      }).not.toThrow();
    });
  });
});

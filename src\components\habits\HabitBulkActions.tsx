import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Alert, Text, TouchableOpacity, View } from 'react-native';
import { Habit } from '../../database/entities/Habit';

interface HabitBulkActionsProps {
  selectedHabits: Habit[];
  onDeleteSelected: (habitIds: string[]) => Promise<void>;
  onArchiveSelected: (habitIds: string[]) => Promise<void>;
  onClearSelection: () => void;
  onSelectAll: () => void;
  totalHabits: number;
  isVisible: boolean;
}

const HabitBulkActions: React.FC<HabitBulkActionsProps> = ({
  selectedHabits,
  onDeleteSelected,
  onArchiveSelected,
  onClearSelection,
  onSelectAll,
  totalHabits,
  isVisible,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleDeleteSelected = () => {
    Alert.alert(
      'Delete Habits',
      `Are you sure you want to delete ${selectedHabits.length} habit${selectedHabits.length > 1 ? 's' : ''}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              await onDeleteSelected(selectedHabits.map(h => h.id));
              onClearSelection();
            } catch {
              Alert.alert('Error', 'Failed to delete habits. Please try again.');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  };

  const handleArchiveSelected = () => {
    Alert.alert(
      'Archive Habits',
      `Are you sure you want to archive ${selectedHabits.length} habit${selectedHabits.length > 1 ? 's' : ''}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Archive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              await onArchiveSelected(selectedHabits.map(h => h.id));
              onClearSelection();
            } catch {
              Alert.alert('Error', 'Failed to archive habits. Please try again.');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  };

  if (!isVisible || selectedHabits.length === 0) {
    return null;
  }

  return (
    <View className="absolute bottom-0 left-0 right-0 border-t border-gray-200 bg-white p-4 shadow-lg dark:border-gray-700 dark:bg-gray-800">
      <View className="mb-4 flex-row items-center justify-between">
        <Text className="text-lg font-bold text-gray-900 dark:text-gray-100">
          {selectedHabits.length} selected
        </Text>
        <TouchableOpacity onPress={onClearSelection}>
          <Ionicons name="close" size={24} color="#6B7280" />
        </TouchableOpacity>
      </View>

      <View className="flex-row space-x-3">
        {/* Select All */}
        {selectedHabits.length < totalHabits && (
          <TouchableOpacity
            onPress={onSelectAll}
            className="flex-1 flex-row items-center justify-center rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20"
          >
            <Ionicons name="checkmark-done" size={20} color="#3B82F6" />
            <Text className="ml-2 font-medium text-blue-600 dark:text-blue-400">Select All</Text>
          </TouchableOpacity>
        )}

        {/* Archive */}
        <TouchableOpacity
          onPress={handleArchiveSelected}
          disabled={isProcessing}
          className="flex-1 flex-row items-center justify-center rounded-lg bg-yellow-50 p-3 dark:bg-yellow-900/20"
        >
          <Ionicons name="archive" size={20} color="#F59E0B" />
          <Text className="ml-2 font-medium text-yellow-600 dark:text-yellow-400">Archive</Text>
        </TouchableOpacity>

        {/* Delete */}
        <TouchableOpacity
          onPress={handleDeleteSelected}
          disabled={isProcessing}
          className="flex-1 flex-row items-center justify-center rounded-lg bg-red-50 p-3 dark:bg-red-900/20"
        >
          <Ionicons name="trash" size={20} color="#EF4444" />
          <Text className="ml-2 font-medium text-red-600 dark:text-red-400">
            {isProcessing ? 'Processing...' : 'Delete'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default HabitBulkActions;

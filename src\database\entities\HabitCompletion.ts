import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('habit_completions')
@Index(['habit_id', 'completed_date'], { unique: true })
export class HabitCompletion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  habit_id: string;

  @Column({ type: 'date' })
  completed_date: string; // Format: YYYY-MM-DD

  @Column({ type: 'integer', default: 1 })
  count: number;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'integer', nullable: true })
  mood_rating?: number; // 1-5 scale

  @CreateDateColumn()
  created_at: Date;

  @ManyToOne('Habit', 'completions', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'habit_id' })
  habit: any;
}

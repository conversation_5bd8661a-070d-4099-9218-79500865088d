import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
  ActionSheetIOS,
  Alert,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { HabitProgressChart, HabitStatsCard } from '../src/components/habits';
import { useHabits } from '../src/hooks/useHabits';
import { habitStatisticsService } from '../src/services/habitStatisticsService';
import { HabitStats } from '../src/types/habit.types';

export default function HabitDetailScreen() {
  const router = useRouter();
  const { habitId } = useLocalSearchParams<{ habitId: string }>();
  const { getHabitById, deleteHabit, toggleHabitCompletion, isHabitCompletedToday } = useHabits();

  const habit = getHabitById(habitId);
  const [isDeleting, setIsDeleting] = useState(false);
  const [stats, setStats] = useState<HabitStats | null>(null);
  const [progressData, setProgressData] = useState<
    { date: string; completed: boolean; count: number }[]
  >([]);
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  const loadHabitStatistics = React.useCallback(async () => {
    if (!habit) return;

    setIsLoadingStats(true);
    try {
      const [habitStats, progressHistory] = await Promise.all([
        habitStatisticsService.calculateHabitStats(habit.id),
        habitStatisticsService.getCompletionHistory(habit.id, 30),
      ]);

      setStats(habitStats);
      setProgressData(progressHistory);
    } catch {
      // Silently handle statistics loading errors
    } finally {
      setIsLoadingStats(false);
    }
  }, [habit]);

  // Load habit statistics
  React.useEffect(() => {
    if (habit) {
      loadHabitStatistics();
    }
  }, [habit, loadHabitStatistics]);

  if (!habit) {
    return (
      <View className="flex-1 items-center justify-center bg-gray-50 dark:bg-gray-900">
        <Ionicons name="alert-circle-outline" size={64} color="#9CA3AF" />
        <Text className="mt-4 text-xl font-semibold text-gray-900 dark:text-gray-100">
          Habit not found
        </Text>
        <TouchableOpacity
          onPress={() => router.back()}
          className="mt-6 rounded-lg bg-blue-500 px-6 py-3"
        >
          <Text className="font-semibold text-white">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const isCompletedToday = isHabitCompletedToday(habitId);
  // Get today's completion count for this specific habit
  const today = new Date().toISOString().split('T')[0];
  const todayCompletion = habit.completions?.find(
    completion => completion.completed_date === today
  );
  const todayCount = todayCompletion?.count || 0;

  const handleEdit = () => {
    router.push(`/habit-form?habitId=${habitId}`);
  };

  const handleDelete = async () => {
    const showActionSheet = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Delete Habit'],
            destructiveButtonIndex: 1,
            cancelButtonIndex: 0,
            title: 'Are you sure you want to delete this habit?',
            message: 'This action cannot be undone.',
          },
          buttonIndex => {
            if (buttonIndex === 1) {
              confirmDelete();
            }
          }
        );
      } else {
        Alert.alert(
          'Delete Habit',
          'Are you sure you want to delete this habit? This action cannot be undone.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Delete', style: 'destructive', onPress: confirmDelete },
          ]
        );
      }
    };

    const confirmDelete = async () => {
      setIsDeleting(true);
      try {
        await deleteHabit(habitId);
        Alert.alert('Success', 'Habit deleted successfully', [
          { text: 'OK', onPress: () => router.back() },
        ]);
      } catch (error) {
        Alert.alert('Error', error instanceof Error ? error.message : 'Failed to delete habit');
      } finally {
        setIsDeleting(false);
      }
    };

    showActionSheet();
  };

  const handleToggleCompletion = async () => {
    try {
      await toggleHabitCompletion(habitId);
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to update habit');
    }
  };

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      <StatusBar style="auto" />

      {/* Header */}
      <View className="bg-white px-6 pb-6 pt-12 shadow-sm dark:bg-gray-800">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#6B7280" />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-900 dark:text-gray-100">Habit Details</Text>
          <TouchableOpacity onPress={handleEdit}>
            <Ionicons name="create-outline" size={24} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1 px-6 pt-6">
        {/* Habit Info Card */}
        <View className="mb-6 rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
          <View className="mb-4 flex-row items-start justify-between">
            <View className="mr-4 flex-1">
              <View className="mb-2 flex-row items-center">
                <View
                  className="mr-3 h-6 w-6 rounded-full"
                  style={{ backgroundColor: habit.color }}
                />
                <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {habit.name}
                </Text>
              </View>

              {habit.description && (
                <Text className="mb-4 text-gray-600 dark:text-gray-400">{habit.description}</Text>
              )}

              <View className="flex-row items-center space-x-4">
                <View className="flex-row items-center">
                  <Ionicons name="calendar" size={16} color="#6B7280" />
                  <Text className="ml-1 text-sm capitalize text-gray-600 dark:text-gray-400">
                    {habit.frequency}
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="flag" size={16} color="#6B7280" />
                  <Text className="ml-1 text-sm text-gray-600 dark:text-gray-400">
                    {habit.target_count} {habit.unit}
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="folder" size={16} color="#6B7280" />
                  <Text className="ml-1 text-sm capitalize text-gray-600 dark:text-gray-400">
                    {habit.category}
                  </Text>
                </View>
              </View>
            </View>

            <TouchableOpacity
              onPress={handleToggleCompletion}
              className={`h-16 w-16 items-center justify-center rounded-full border-2 ${
                isCompletedToday
                  ? 'border-green-500 bg-green-500'
                  : 'border-gray-300 dark:border-gray-600'
              }`}
            >
              {isCompletedToday && <Ionicons name="checkmark" size={32} color="white" />}
            </TouchableOpacity>
          </View>

          {/* Today's Progress */}
          <View className="rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
            <Text className="mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">
              Today&apos;s Progress
            </Text>
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {todayCount}/{habit.target_count} {habit.unit}
              </Text>
              <Text className="text-sm text-gray-600 dark:text-gray-400">
                {Math.round((todayCount / habit.target_count) * 100)}% complete
              </Text>
            </View>
            <View className="mt-2 h-2 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-600">
              <View
                className="h-full rounded-full bg-green-500"
                style={{ width: `${Math.min((todayCount / habit.target_count) * 100, 100)}%` }}
              />
            </View>
          </View>
        </View>

        {/* Statistics */}
        {stats && !isLoadingStats ? (
          <HabitStatsCard stats={stats} title="Statistics" />
        ) : (
          <View className="mb-6 rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
            <Text className="mb-4 text-lg font-bold text-gray-900 dark:text-gray-100">
              Statistics
            </Text>
            <View className="flex-row justify-center">
              <Text className="text-gray-600 dark:text-gray-400">Loading statistics...</Text>
            </View>
          </View>
        )}

        {/* Progress Chart */}
        {progressData.length > 0 && !isLoadingStats && (
          <View className="mb-6">
            <HabitProgressChart data={progressData} title="30-Day Progress" showDates={false} />
          </View>
        )}

        {/* Actions */}
        <View className="mb-6 rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
          <Text className="mb-4 text-lg font-bold text-gray-900 dark:text-gray-100">Actions</Text>

          <TouchableOpacity
            onPress={handleEdit}
            className="mb-3 flex-row items-center rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20"
          >
            <Ionicons name="create-outline" size={24} color="#3B82F6" />
            <Text className="ml-3 font-medium text-blue-600 dark:text-blue-400">Edit Habit</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleDelete}
            disabled={isDeleting}
            className="flex-row items-center rounded-lg bg-red-50 p-4 dark:bg-red-900/20"
          >
            <Ionicons name="trash-outline" size={24} color="#EF4444" />
            <Text className="ml-3 font-medium text-red-600 dark:text-red-400">
              {isDeleting ? 'Deleting...' : 'Delete Habit'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

interface EmptyStateProps {
  icon?: keyof typeof Ionicons.glyphMap;
  title: string;
  description: string;
  actionText?: string;
  onAction?: () => void;
  illustration?: React.ReactNode;
  compact?: boolean;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'document-outline',
  title,
  description,
  actionText,
  onAction,
  illustration,
  compact = false,
}) => {
  return (
    <View className={`items-center justify-center ${compact ? 'py-8' : 'py-16'} px-6`}>
      {/* Icon or Illustration */}
      {illustration || (
        <View
          className={`${compact ? 'h-16 w-16' : 'h-24 w-24'} mb-4 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800`}
        >
          <Ionicons name={icon} size={compact ? 32 : 48} color="#9CA3AF" />
        </View>
      )}

      {/* Title */}
      <Text
        className={`${compact ? 'text-lg' : 'text-xl'} mb-2 text-center font-bold text-gray-900 dark:text-gray-100`}
      >
        {title}
      </Text>

      {/* Description */}
      <Text
        className={`${compact ? 'text-sm' : 'text-base'} mb-6 max-w-sm text-center text-gray-600 dark:text-gray-400`}
      >
        {description}
      </Text>

      {/* Action Button */}
      {actionText && onAction && (
        <TouchableOpacity onPress={onAction} className="rounded-xl bg-blue-500 px-6 py-3">
          <Text className="font-semibold text-white">{actionText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default EmptyState;

/**
 * Type definitions for habit-related data structures
 */

export type HabitFrequency = 'daily' | 'weekly' | 'monthly';

export type HabitCategory =
  | 'health'
  | 'fitness'
  | 'productivity'
  | 'learning'
  | 'mindfulness'
  | 'social'
  | 'creativity'
  | 'finance'
  | 'other';

export type HabitUnit =
  | 'times'
  | 'minutes'
  | 'hours'
  | 'pages'
  | 'glasses'
  | 'steps'
  | 'kilometers'
  | 'miles'
  | 'pounds'
  | 'kilograms'
  | 'other';

export interface HabitSubTask {
  id?: string;
  name: string;
  description: string;
  isCompleted?: boolean;
  order: number;
}

export interface HabitTimeRange {
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  isFlexible: boolean; // Whether the time range is flexible or strict
}

export interface HabitFormData {
  name: string;
  description?: string;
  category: HabitCategory;
  frequency: HabitFrequency;
  target_count: number;
  unit: HabitUnit;
  color: string;
  reminder_time?: string;
  reminder_enabled?: boolean;
  // New hierarchical fields
  purpose: string;
  timeRange?: HabitTimeRange;
  subTasks: HabitSubTask[];
}

export interface HabitCompletionData {
  habit_id: string;
  completed_date: string;
  count?: number;
  notes?: string;
  mood_rating?: number;
}

export interface HabitStreakData {
  habit_id: string;
  start_date: string;
  end_date?: string;
  length: number;
  is_current: boolean;
}

export interface HabitStats {
  totalCompletions: number;
  currentStreak: number;
  longestStreak: number;
  completionRate: number;
  averageCompletionsPerWeek: number;
  averageCompletionsPerMonth: number;
  lastCompletedDate?: string;
}

export interface HabitWithStats {
  id: string;
  name: string;
  description?: string;
  category: HabitCategory;
  frequency: HabitFrequency;
  target_count: number;
  unit: HabitUnit;
  color: string;
  is_active: boolean;
  sort_order: number;
  reminder_time?: string;
  reminder_enabled: boolean;
  user_id: string;
  created_at: string;
  updated_at: string;
  stats: HabitStats;
}

export interface HabitFilters {
  category?: HabitCategory;
  frequency?: HabitFrequency;
  isActive?: boolean;
  hasReminder?: boolean;
}

export interface HabitSortOptions {
  field: 'name' | 'created_at' | 'updated_at' | 'sort_order' | 'category';
  direction: 'asc' | 'desc';
}

export interface HabitAnalytics {
  totalHabits: number;
  activeHabits: number;
  completedToday: number;
  completionRateToday: number;
  averageStreakLength: number;
  mostCompletedHabit?: {
    id: string;
    name: string;
    completions: number;
  };
  longestStreakHabit?: {
    id: string;
    name: string;
    streak: number;
  };
  categoryBreakdown: {
    category: HabitCategory;
    count: number;
    percentage: number;
  }[];
  weeklyProgress: {
    date: string;
    completions: number;
    target: number;
  }[];
  monthlyProgress: {
    month: string;
    completions: number;
    target: number;
  }[];
}

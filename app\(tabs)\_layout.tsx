import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React from 'react';
import { useResolvedTheme } from '../../src/components';

export default function TabLayout() {
  const resolvedTheme = useResolvedTheme();

  const backgroundColor = resolvedTheme === 'dark' ? '#111827' : '#F9FAFB';

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: resolvedTheme === 'dark' ? '#3B82F6' : '#2563EB',
        tabBarInactiveTintColor: resolvedTheme === 'dark' ? '#9CA3AF' : '#6B7280',
        tabBarStyle: {
          backgroundColor: resolvedTheme === 'dark' ? '#1F2937' : '#FFFFFF',
          borderTopColor: resolvedTheme === 'dark' ? '#374151' : '#E5E7EB',
          height: 85,
          paddingBottom: 8,
          paddingTop: 8,
        },
        headerStyle: {
          backgroundColor: resolvedTheme === 'dark' ? '#1F2937' : '#FFFFFF',
        },
        headerTintColor: resolvedTheme === 'dark' ? '#FFFFFF' : '#000000',
        sceneStyle: { backgroundColor },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="habits"
        options={{
          title: 'Habit',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="checkmark-circle-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="more"
        options={{
          title: 'More',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="ellipsis-horizontal" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="pomodoro"
        options={{
          title: 'Pomodoro',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="timer-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}

import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React from 'react';
import { useResolvedTheme } from '../../src/components';

export default function TabLayout() {
  const resolvedTheme = useResolvedTheme();

  const backgroundColor = resolvedTheme === 'dark' ? '#111827' : '#F9FAFB';

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: resolvedTheme === 'dark' ? '#3B82F6' : '#2563EB',
        tabBarInactiveTintColor: resolvedTheme === 'dark' ? '#9CA3AF' : '#6B7280',
        tabBarStyle: {
          backgroundColor: resolvedTheme === 'dark' ? '#1F2937' : '#FFFFFF',
          borderTopColor: resolvedTheme === 'dark' ? '#374151' : '#E5E7EB',
          height: 85,
          paddingBottom: 8,
          paddingTop: 8,
          // Add rounded corners to left and right edges
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          // Add bottom margin for spacing from screen edge
          marginBottom: 10,
          marginLeft: 10,
          marginRight: 10,
          // Add subtle shadow for better visual separation
          shadowColor: resolvedTheme === 'dark' ? '#000000' : '#000000',
          shadowOffset: {
            width: 0,
            height: -2,
          },
          shadowOpacity: resolvedTheme === 'dark' ? 0.3 : 0.1,
          shadowRadius: 8,
          elevation: 8,
        },
        // Hide the header titles completely
        headerShown: false,
        sceneStyle: { backgroundColor },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="habits"
        options={{
          title: 'Habit',
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="checkmark-circle-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="more"
        options={{
          title: 'More',
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="ellipsis-horizontal" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="pomodoro"
        options={{
          title: 'Pomodoro',
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="timer-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}

/**
 * Application constants for the WinArc app
 */

// App Information
export const APP_NAME = 'WinArc: Habit Tracker';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'Build lasting habits, track your progress, and achieve your goals';

// Colors
export const COLORS = {
  primary: '#3B82F6',
  secondary: '#6B7280',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#06B6D4',

  // Habit colors
  habitColors: [
    '#EF4444', // red
    '#F97316', // orange
    '#F59E0B', // amber
    '#EAB308', // yellow
    '#84CC16', // lime
    '#22C55E', // green
    '#10B981', // emerald
    '#14B8A6', // teal
    '#06B6D4', // cyan
    '#0EA5E9', // sky
    '#3B82F6', // blue
    '#6366F1', // indigo
    '#8B5CF6', // violet
    '#A855F7', // purple
    '#D946EF', // fuchsia
    '#EC4899', // pink
    '#F43F5E', // rose
  ],
};

// Habit Categories
export const HABIT_CATEGORIES = [
  { value: 'health', label: 'Health', icon: 'heart' },
  { value: 'fitness', label: 'Fitness', icon: 'fitness' },
  { value: 'productivity', label: 'Productivity', icon: 'briefcase' },
  { value: 'learning', label: 'Learning', icon: 'book' },
  { value: 'mindfulness', label: 'Mindfulness', icon: 'leaf' },
  { value: 'social', label: 'Social', icon: 'people' },
  { value: 'creativity', label: 'Creativity', icon: 'brush' },
  { value: 'finance', label: 'Finance', icon: 'card' },
  { value: 'other', label: 'Other', icon: 'ellipsis-horizontal' },
];

// Habit Frequencies
export const HABIT_FREQUENCIES = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
];

// Habit Units
export const HABIT_UNITS = [
  { value: 'times', label: 'Times' },
  { value: 'minutes', label: 'Minutes' },
  { value: 'hours', label: 'Hours' },
  { value: 'pages', label: 'Pages' },
  { value: 'glasses', label: 'Glasses' },
  { value: 'steps', label: 'Steps' },
  { value: 'kilometers', label: 'Kilometers' },
  { value: 'miles', label: 'Miles' },
  { value: 'pounds', label: 'Pounds' },
  { value: 'kilograms', label: 'Kilograms' },
  { value: 'other', label: 'Other' },
];

// Timer Settings
export const TIMER_DEFAULTS = {
  pomodoroWork: 25, // minutes
  pomodoroBreak: 5, // minutes
  pomodoroLongBreak: 15, // minutes
  pomodoroSessionsUntilLongBreak: 4,
  countdownDefault: 25, // minutes
};

// Themes
export const THEMES = [
  { value: 'light', label: 'Light' },
  { value: 'dark', label: 'Dark' },
  { value: 'system', label: 'System' },
];

// Languages
export const LANGUAGES = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Español' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'zh', label: '中文' },
  { value: 'ja', label: '日本語' },
];

// Mood Ratings
export const MOOD_RATINGS = [
  { value: 1, label: 'Very Bad', emoji: '😞', color: '#EF4444' },
  { value: 2, label: 'Bad', emoji: '😕', color: '#F97316' },
  { value: 3, label: 'Neutral', emoji: '😐', color: '#F59E0B' },
  { value: 4, label: 'Good', emoji: '🙂', color: '#84CC16' },
  { value: 5, label: 'Very Good', emoji: '😊', color: '#22C55E' },
];

// Database Constants
export const DATABASE_NAME = 'winarc.db';
export const DATABASE_VERSION = 1;

// Storage Keys
export const STORAGE_KEYS = {
  userStore: 'user-store',
  appStore: 'app-store',
  onboardingCompleted: 'onboarding-completed',
  firstLaunch: 'first-launch',
};

// API Endpoints (for future use)
export const API_ENDPOINTS = {
  base: 'https://api.winarc.app',
  auth: '/auth',
  habits: '/habits',
  users: '/users',
  sync: '/sync',
};

// Validation Limits
export const VALIDATION_LIMITS = {
  habitName: { min: 1, max: 100 },
  habitDescription: { max: 500 },
  userName: { min: 2, max: 50 },
  notes: { max: 1000 },
  targetCount: { min: 1, max: 1000 },
  timerDuration: { min: 1, max: 180 }, // minutes
};

// Animation Durations (milliseconds)
export const ANIMATION_DURATIONS = {
  short: 200,
  medium: 300,
  long: 500,
};

// Screen Dimensions
export const SCREEN_BREAKPOINTS = {
  small: 320,
  medium: 768,
  large: 1024,
  extraLarge: 1280,
};

// Default Values
export const DEFAULTS = {
  habitColor: COLORS.habitColors[0],
  habitCategory: 'other',
  habitFrequency: 'daily',
  habitUnit: 'times',
  habitTargetCount: 1,
  theme: 'system',
  language: 'en',
  notificationsEnabled: true,
  reminderNotificationsEnabled: true,
};

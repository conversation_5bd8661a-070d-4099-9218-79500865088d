import { Ionicons } from '@expo/vector-icons';
import { useEffect } from 'react';
import { ScrollView, Switch, Text, TouchableOpacity, View } from 'react-native';
import { useTheme } from '../../src/components';
import { useAppStore, useUserStore } from '../../src/stores';

export default function ProfileScreen() {
  const { currentUser } = useUserStore();
  const {
    notificationsEnabled,
    reminderNotificationsEnabled,
    setActiveTab,
    setNotificationsEnabled,
    setReminderNotificationsEnabled,
  } = useAppStore();

  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setActiveTab('profile');
  }, [setActiveTab]);

  const ProfileSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View className="mb-6">
      <Text className="mb-3 px-6 text-lg font-semibold text-gray-900">{title}</Text>
      <View className="bg-white">{children}</View>
    </View>
  );

  const ProfileItem = ({
    icon,
    title,
    subtitle,
    onPress,
    rightElement,
  }: {
    icon: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className="flex-row items-center border-b border-gray-100 px-6 py-4"
      disabled={!onPress}
    >
      <View className="mr-4 h-10 w-10 items-center justify-center rounded-full bg-gray-100">
        <Ionicons name={icon as any} size={20} color="#6B7280" />
      </View>
      <View className="flex-1">
        <Text className="text-base font-medium text-gray-900">{title}</Text>
        {subtitle && <Text className="mt-1 text-sm text-gray-600">{subtitle}</Text>}
      </View>
      {rightElement || (onPress && <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />)}
    </TouchableOpacity>
  );

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-6 pb-6 pt-12 shadow-sm">
        <View className="flex-row items-center">
          <View className="mr-4 h-16 w-16 items-center justify-center rounded-full bg-blue-500">
            <Text className="text-2xl font-bold text-white">
              {currentUser?.name?.charAt(0) || 'U'}
            </Text>
          </View>
          <View className="flex-1">
            <Text className="text-xl font-bold text-gray-900">{currentUser?.name || 'User'}</Text>
            <Text className="text-gray-600">{currentUser?.email || '<EMAIL>'}</Text>
          </View>
          <TouchableOpacity className="h-10 w-10 items-center justify-center rounded-full bg-gray-100">
            <Ionicons name="pencil" size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1 pt-6">
        {/* Account Settings */}
        <ProfileSection title="Account">
          <ProfileItem
            icon="person-outline"
            title="Edit Profile"
            subtitle="Update your personal information"
            onPress={() => {}}
          />
          <ProfileItem
            icon="key-outline"
            title="Privacy & Security"
            subtitle="Manage your account security"
            onPress={() => {}}
          />
        </ProfileSection>

        {/* App Settings */}
        <ProfileSection title="Preferences">
          <ProfileItem
            icon="color-palette-outline"
            title="Theme"
            subtitle={`Current: ${theme.charAt(0).toUpperCase() + theme.slice(1)}`}
            onPress={() => {
              const themes = ['light', 'dark', 'system'];
              const currentIndex = themes.indexOf(theme);
              const nextTheme = themes[(currentIndex + 1) % themes.length];
              setTheme(nextTheme as any);
            }}
          />
          <ProfileItem
            icon="language-outline"
            title="Language"
            subtitle="English"
            onPress={() => {}}
          />
          <ProfileItem
            icon="notifications-outline"
            title="Notifications"
            subtitle="Receive app notifications"
            rightElement={
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
                thumbColor={notificationsEnabled ? '#FFFFFF' : '#FFFFFF'}
              />
            }
          />
          <ProfileItem
            icon="alarm-outline"
            title="Habit Reminders"
            subtitle="Get reminded about your habits"
            rightElement={
              <Switch
                value={reminderNotificationsEnabled}
                onValueChange={setReminderNotificationsEnabled}
                trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
                thumbColor={reminderNotificationsEnabled ? '#FFFFFF' : '#FFFFFF'}
              />
            }
          />
        </ProfileSection>

        {/* Data & Analytics */}
        <ProfileSection title="Data">
          <ProfileItem
            icon="stats-chart-outline"
            title="Analytics"
            subtitle="View your habit statistics"
            onPress={() => {}}
          />
          <ProfileItem
            icon="download-outline"
            title="Export Data"
            subtitle="Download your habit data"
            onPress={() => {}}
          />
          <ProfileItem
            icon="cloud-upload-outline"
            title="Backup & Sync"
            subtitle="Sync your data across devices"
            onPress={() => {}}
          />
        </ProfileSection>

        {/* Support */}
        <ProfileSection title="Support">
          <ProfileItem
            icon="help-circle-outline"
            title="Help & FAQ"
            subtitle="Get help with using WinArc"
            onPress={() => {}}
          />
          <ProfileItem
            icon="mail-outline"
            title="Contact Support"
            subtitle="Get in touch with our team"
            onPress={() => {}}
          />
          <ProfileItem
            icon="star-outline"
            title="Rate WinArc"
            subtitle="Share your feedback"
            onPress={() => {}}
          />
        </ProfileSection>

        {/* About */}
        <ProfileSection title="About">
          <ProfileItem
            icon="information-circle-outline"
            title="About WinArc"
            subtitle="Version 1.0.0"
            onPress={() => {}}
          />
          <ProfileItem icon="document-text-outline" title="Terms of Service" onPress={() => {}} />
          <ProfileItem icon="shield-checkmark-outline" title="Privacy Policy" onPress={() => {}} />
        </ProfileSection>

        <View className="px-6 py-6">
          <TouchableOpacity className="items-center rounded-xl bg-red-500 py-4">
            <Text className="font-semibold text-white">Sign Out</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

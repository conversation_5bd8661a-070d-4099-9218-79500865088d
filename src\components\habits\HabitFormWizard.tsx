import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { HabitFormData } from '../../types/habit.types';
import HabitFormStep1 from './HabitFormStep1';
import HabitFormStep2 from './HabitFormStep2';
import HabitFormStep3 from './HabitFormStep3';
import HabitFormStep4 from './HabitFormStep4';

interface HabitFormWizardProps {
  initialData?: Partial<HabitFormData>;
  onSubmit: (data: HabitFormData) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
  isLoading?: boolean;
}

const steps = [
  { id: 1, title: 'Basic Info', description: 'Name and purpose' },
  { id: 2, title: 'Schedule', description: 'Time and frequency' },
  { id: 3, title: 'Sub-tasks', description: 'Break it down' },
  { id: 4, title: 'Review', description: 'Confirm details' },
];

const HabitFormWizard: React.FC<HabitFormWizardProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isEditing = false,
  isLoading = false,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<HabitFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    category: initialData?.category || 'other',
    frequency: initialData?.frequency || 'daily',
    target_count: initialData?.target_count || 1,
    unit: initialData?.unit || 'times',
    color: initialData?.color || '#3B82F6',
    reminder_time: initialData?.reminder_time || '',
    reminder_enabled: initialData?.reminder_enabled || false,
    purpose: initialData?.purpose || '',
    timeRange: initialData?.timeRange,
    subTasks: initialData?.subTasks || [],
  });

  const [errors, setErrors] = useState<Partial<Record<keyof HabitFormData, string>>>({});

  const updateFormData = <K extends keyof HabitFormData>(key: K, value: HabitFormData[K]) => {
    setFormData(prev => ({ ...prev, [key]: value }));
    // Clear error for this field when user starts typing
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: undefined }));
    }
  };

  const validateCurrentStep = (): boolean => {
    const newErrors: Partial<Record<keyof HabitFormData, string>> = {};

    switch (currentStep) {
      case 1:
        if (!formData.name.trim()) {
          newErrors.name = 'Habit name is required';
        } else if (formData.name.trim().length < 2) {
          newErrors.name = 'Habit name must be at least 2 characters';
        } else if (formData.name.trim().length > 100) {
          newErrors.name = 'Habit name must be less than 100 characters';
        }

        if (!formData.purpose.trim()) {
          newErrors.purpose = 'Purpose is required';
        } else if (formData.purpose.trim().length < 10) {
          newErrors.purpose = 'Purpose must be at least 10 characters';
        } else if (formData.purpose.length > 300) {
          newErrors.purpose = 'Purpose must be less than 300 characters';
        }
        break;

      case 2:
        if (formData.target_count < 1) {
          newErrors.target_count = 'Target count must be at least 1';
        } else if (formData.target_count > 100) {
          newErrors.target_count = 'Target count must be less than 100';
        }
        break;

      case 3:
        // Sub-tasks are optional, but if provided, validate them
        for (const task of formData.subTasks) {
          if (!task.name.trim()) {
            newErrors.subTasks = 'All sub-tasks must have a name';
            break;
          }
          if (task.name.length > 100) {
            newErrors.subTasks = 'Sub-task names must be less than 100 characters';
            break;
          }
          if (task.description && task.description.length > 200) {
            newErrors.subTasks = 'Sub-task descriptions must be less than 200 characters';
            break;
          }
        }
        break;

      case 4:
        // Final validation - all previous steps should be valid
        return validateCurrentStep();
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to save habit');
    }
  };

  const renderStepIndicator = () => (
    <View className="mb-6 flex-row items-center justify-center">
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <View className="items-center">
            <View
              className={`h-10 w-10 items-center justify-center rounded-full ${
                currentStep >= step.id ? 'bg-blue-500' : 'bg-gray-200 dark:bg-gray-700'
              }`}
            >
              {currentStep > step.id ? (
                <Ionicons name="checkmark" size={20} color="white" />
              ) : (
                <Text
                  className={`font-semibold ${
                    currentStep >= step.id ? 'text-white' : 'text-gray-500 dark:text-gray-400'
                  }`}
                >
                  {step.id}
                </Text>
              )}
            </View>
            <Text
              className={`mt-1 text-xs ${
                currentStep >= step.id
                  ? 'font-medium text-blue-500'
                  : 'text-gray-500 dark:text-gray-400'
              }`}
            >
              {step.title}
            </Text>
          </View>
          {index < steps.length - 1 && (
            <View
              className={`mx-2 h-0.5 flex-1 ${
                currentStep > step.id ? 'bg-blue-500' : 'bg-gray-200 dark:bg-gray-700'
              }`}
            />
          )}
        </React.Fragment>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <HabitFormStep1 formData={formData} errors={errors} updateFormData={updateFormData} />
        );
      case 2:
        return (
          <HabitFormStep2 formData={formData} errors={errors} updateFormData={updateFormData} />
        );
      case 3:
        return (
          <HabitFormStep3 formData={formData} errors={errors} updateFormData={updateFormData} />
        );
      case 4:
        return <HabitFormStep4 formData={formData} onEdit={step => setCurrentStep(step)} />;
      default:
        return null;
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
    >
      <ScrollView className="flex-1 bg-gray-50 dark:bg-gray-900">
        <View className="p-6">
          {/* Header */}
          <View className="mb-6 flex-row items-center justify-between">
            <TouchableOpacity onPress={onCancel}>
              <Ionicons name="close" size={24} color="#6B7280" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-gray-900 dark:text-gray-100">
              {isEditing ? 'Edit Habit' : 'New Habit'}
            </Text>
            <View className="w-6" />
          </View>

          {/* Step Indicator */}
          {renderStepIndicator()}

          {/* Current Step Content */}
          {renderCurrentStep()}
        </View>
      </ScrollView>

      {/* Navigation Buttons */}
      <View className="border-t border-gray-200 bg-white px-6 py-4 dark:border-gray-700 dark:bg-gray-800">
        <View className="flex-row justify-between">
          <TouchableOpacity
            onPress={handlePrevious}
            disabled={currentStep === 1}
            className={`rounded-xl px-6 py-3 ${
              currentStep === 1 ? 'bg-gray-200 dark:bg-gray-700' : 'bg-gray-300 dark:bg-gray-600'
            }`}
          >
            <Text
              className={`font-medium ${
                currentStep === 1
                  ? 'text-gray-400 dark:text-gray-500'
                  : 'text-gray-700 dark:text-gray-300'
              }`}
            >
              Previous
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={currentStep === steps.length ? handleSubmit : handleNext}
            disabled={isLoading}
            className={`rounded-xl px-6 py-3 ${
              isLoading ? 'bg-gray-300 dark:bg-gray-700' : 'bg-blue-500'
            }`}
          >
            <Text className="font-medium text-white">
              {isLoading
                ? 'Saving...'
                : currentStep === steps.length
                  ? isEditing
                    ? 'Update Habit'
                    : 'Create Habit'
                  : 'Next'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

export default HabitFormWizard;

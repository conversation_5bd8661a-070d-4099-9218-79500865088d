import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import { HabitFormData, HabitSubTask } from '../../types/habit.types';

interface HabitFormStep3Props {
  formData: HabitFormData;
  errors: Partial<Record<keyof HabitFormData, string>>;
  updateFormData: <K extends keyof HabitFormData>(key: K, value: HabitFormData[K]) => void;
}

const HabitFormStep3: React.FC<HabitFormStep3Props> = ({ formData, errors, updateFormData }) => {
  const addSubTask = () => {
    const newSubTask: HabitSubTask = {
      id: Date.now().toString(),
      name: '',
      description: '',
      isCompleted: false,
      order: formData.subTasks.length,
    };
    updateFormData('subTasks', [...formData.subTasks, newSubTask]);
  };

  const updateSubTask = (index: number, field: keyof HabitSubTask, value: string | boolean) => {
    const updatedSubTasks = formData.subTasks.map((task, i) =>
      i === index ? { ...task, [field]: value } : task
    );
    updateFormData('subTasks', updatedSubTasks);
  };

  const removeSubTask = (index: number) => {
    const updatedSubTasks = formData.subTasks.filter((_, i) => i !== index);
    updateFormData('subTasks', updatedSubTasks);
  };

  const moveSubTask = (fromIndex: number, toIndex: number) => {
    const updatedSubTasks = [...formData.subTasks];
    const [movedTask] = updatedSubTasks.splice(fromIndex, 1);
    updatedSubTasks.splice(toIndex, 0, movedTask);
    updateFormData('subTasks', updatedSubTasks);
  };

  return (
    <View className="space-y-6">
      {/* Step Title */}
      <View className="mb-6 text-center">
        <Text className="mb-2 text-2xl font-bold text-gray-900 dark:text-gray-100">
          Break It Down
        </Text>
        <Text className="text-gray-600 dark:text-gray-400">
          Divide your habit into smaller, actionable steps (optional but recommended)
        </Text>
      </View>

      {/* Benefits Info */}
      <View className="mb-4 rounded-xl bg-green-50 p-4 dark:bg-green-900">
        <View className="flex-row items-start">
          <Ionicons name="bulb-outline" size={20} color="#10B981" />
          <View className="ml-3 flex-1">
            <Text className="mb-1 font-medium text-green-800 dark:text-green-200">
              Why break it down?
            </Text>
            <Text className="text-sm text-green-700 dark:text-green-300">
              Sub-tasks make habits easier to start, track progress more precisely, and maintain
              consistency.
            </Text>
          </View>
        </View>
      </View>

      {/* Sub-tasks List */}
      <View>
        <View className="mb-4 flex-row items-center justify-between">
          <Text className="text-base font-medium text-gray-900 dark:text-gray-100">
            Sub-tasks ({formData.subTasks.length})
          </Text>
          <TouchableOpacity
            onPress={addSubTask}
            className="flex-row items-center rounded-lg bg-blue-500 px-4 py-2"
          >
            <Ionicons name="add" size={16} color="white" />
            <Text className="ml-1 font-medium text-white">Add Step</Text>
          </TouchableOpacity>
        </View>

        {formData.subTasks.length === 0 ? (
          <View className="items-center rounded-xl bg-gray-100 p-6 dark:bg-gray-800">
            <Ionicons name="list-outline" size={40} color="#9CA3AF" />
            <Text className="mt-2 text-center text-gray-600 dark:text-gray-400">
              No sub-tasks yet
            </Text>
            <Text className="mt-1 text-center text-sm text-gray-500 dark:text-gray-500">
              Add steps to break down your habit into manageable pieces
            </Text>
          </View>
        ) : (
          <View className="space-y-3">
            {formData.subTasks.map((task, index) => (
              <View
                key={task.id}
                className="rounded-xl border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
              >
                {/* Sub-task Header */}
                <View className="mb-3 flex-row items-center justify-between">
                  <Text className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Step {index + 1}
                  </Text>
                  <View className="flex-row items-center space-x-2">
                    {/* Move Up */}
                    {index > 0 && (
                      <TouchableOpacity
                        onPress={() => moveSubTask(index, index - 1)}
                        className="h-8 w-8 items-center justify-center"
                      >
                        <Ionicons name="chevron-up" size={16} color="#6B7280" />
                      </TouchableOpacity>
                    )}
                    {/* Move Down */}
                    {index < formData.subTasks.length - 1 && (
                      <TouchableOpacity
                        onPress={() => moveSubTask(index, index + 1)}
                        className="h-8 w-8 items-center justify-center"
                      >
                        <Ionicons name="chevron-down" size={16} color="#6B7280" />
                      </TouchableOpacity>
                    )}
                    {/* Remove */}
                    <TouchableOpacity
                      onPress={() => removeSubTask(index)}
                      className="h-8 w-8 items-center justify-center"
                    >
                      <Ionicons name="trash-outline" size={16} color="#EF4444" />
                    </TouchableOpacity>
                  </View>
                </View>

                {/* Sub-task Name */}
                <TextInput
                  value={task.name}
                  onChangeText={text => updateSubTask(index, 'name', text)}
                  placeholder="e.g., Fill water bottle"
                  placeholderTextColor="#9CA3AF"
                  className="mb-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
                  maxLength={100}
                />

                {/* Sub-task Description */}
                <TextInput
                  value={task.description || ''}
                  onChangeText={text => updateSubTask(index, 'description', text)}
                  placeholder="Optional description..."
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={2}
                  className="rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
                  style={{ textAlignVertical: 'top' }}
                  maxLength={200}
                />
              </View>
            ))}
          </View>
        )}

        {errors.subTasks && <Text className="mt-2 text-sm text-red-500">{errors.subTasks}</Text>}
      </View>

      {/* Examples */}
      {formData.subTasks.length === 0 && (
        <View className="rounded-xl bg-blue-50 p-4 dark:bg-blue-900">
          <Text className="mb-2 font-medium text-blue-800 dark:text-blue-200">
            Example sub-tasks for &quot;Drink 8 glasses of water&quot;:
          </Text>
          <View className="space-y-1">
            {[
              '1. Fill water bottle in the morning',
              '2. Drink one glass before each meal',
              '3. Keep water bottle visible on desk',
              '4. Set hourly reminders',
            ].map((example, index) => (
              <Text key={index} className="text-sm text-blue-700 dark:text-blue-300">
                {example}
              </Text>
            ))}
          </View>
        </View>
      )}

      {/* Progress Indicator */}
      <View className="mt-6 rounded-xl bg-blue-50 p-4 dark:bg-blue-900">
        <View className="flex-row items-center">
          <View className="mr-3 h-3 w-3 rounded-full bg-blue-500" />
          <Text className="font-medium text-blue-700 dark:text-blue-300">
            Step 3 of 4: Sub-tasks
          </Text>
        </View>
        <Text className="mt-2 text-sm text-blue-600 dark:text-blue-400">
          Almost done! Let&apos;s review everything before creating your habit.
        </Text>
      </View>
    </View>
  );
};

export default HabitFormStep3;

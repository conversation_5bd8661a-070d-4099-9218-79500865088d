import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { HabitCategory } from '../../types/habit.types';

interface HabitCategoryPickerProps {
  selectedCategory: HabitCategory;
  onCategorySelect: (category: HabitCategory) => void;
  label?: string;
}

const CATEGORY_CONFIG: Record<
  HabitCategory,
  { label: string; icon: keyof typeof Ionicons.glyphMap }
> = {
  health: { label: 'Health', icon: 'heart' },
  fitness: { label: 'Fitness', icon: 'barbell' },
  productivity: { label: 'Productivity', icon: 'briefcase' },
  learning: { label: 'Learning', icon: 'book' },
  mindfulness: { label: 'Mindfulness', icon: 'leaf' },
  social: { label: 'Social', icon: 'people' },
  creativity: { label: 'Creativity', icon: 'color-palette' },
  finance: { label: 'Finance', icon: 'card' },
  other: { label: 'Other', icon: 'ellipsis-horizontal' },
};

const HabitCategoryPicker: React.FC<HabitCategoryPickerProps> = ({
  selectedCategory,
  onCategorySelect,
  label = 'Category',
}) => {
  return (
    <View className="mb-4">
      <Text className="mb-3 text-base font-medium text-gray-900 dark:text-gray-100">{label}</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        className="flex-row"
        contentContainerStyle={{ paddingRight: 16 }}
      >
        {Object.entries(CATEGORY_CONFIG).map(([category, config]) => {
          const isSelected = selectedCategory === category;
          return (
            <TouchableOpacity
              key={category}
              onPress={() => onCategorySelect(category as HabitCategory)}
              className={`mr-3 min-w-[100px] flex-row items-center rounded-xl border-2 px-4 py-3 ${
                isSelected
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
              }`}
            >
              <Ionicons
                name={config.icon}
                size={20}
                color={isSelected ? '#3B82F6' : '#6B7280'}
                style={{ marginRight: 8 }}
              />
              <Text
                className={`text-sm font-medium ${
                  isSelected
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-700 dark:text-gray-300'
                }`}
              >
                {config.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

export default HabitCategoryPicker;

import React from 'react';
import { ScrollView, Text, View } from 'react-native';
interface HabitProgressChartProps {
  data: { date: string; completed: boolean; count: number }[];
  title?: string;
  showDates?: boolean;
  compact?: boolean;
}

const HabitProgressChart: React.FC<HabitProgressChartProps> = ({
  data,
  title = 'Progress',
  showDates = false,
  compact = false,
}) => {
  const maxCount = Math.max(...data.map(d => d.count), 1);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const getDayOfWeek = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  };

  if (compact) {
    return (
      <View className="mb-4">
        <Text className="mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">{title}</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row space-x-1">
            {data.map((item, index) => (
              <View key={index} className="items-center">
                <View
                  className={`h-6 w-6 rounded ${
                    item.completed ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                />
                {showDates && (
                  <Text className="mt-1 text-xs text-gray-600 dark:text-gray-400">
                    {getDayOfWeek(item.date)}
                  </Text>
                )}
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View className="mb-6">
      <Text className="mb-4 text-lg font-bold text-gray-900 dark:text-gray-100">{title}</Text>

      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View className="flex-row items-end space-x-2 pb-4">
          {data.map((item, index) => {
            const height = Math.max((item.count / maxCount) * 100, 4);

            return (
              <View key={index} className="items-center">
                {/* Bar */}
                <View className="relative mb-2" style={{ height: 120 }}>
                  <View
                    className={`w-8 rounded-t ${
                      item.completed ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                    style={{
                      height: `${height}%`,
                      position: 'absolute',
                      bottom: 0,
                      width: 32,
                    }}
                  />

                  {/* Count label */}
                  {item.count > 0 && (
                    <View className="absolute -top-6 left-1/2 -translate-x-1/2 transform">
                      <Text className="text-xs font-medium text-gray-900 dark:text-gray-100">
                        {item.count}
                      </Text>
                    </View>
                  )}
                </View>

                {/* Date label */}
                <Text className="text-center text-xs text-gray-600 dark:text-gray-400">
                  {showDates ? formatDate(item.date) : getDayOfWeek(item.date)}
                </Text>
              </View>
            );
          })}
        </View>
      </ScrollView>

      {/* Legend */}
      <View className="mt-4 flex-row items-center justify-center space-x-4">
        <View className="flex-row items-center">
          <View className="mr-2 h-3 w-3 rounded bg-green-500" />
          <Text className="text-sm text-gray-600 dark:text-gray-400">Completed</Text>
        </View>
        <View className="flex-row items-center">
          <View className="mr-2 h-3 w-3 rounded bg-gray-200 dark:bg-gray-700" />
          <Text className="text-sm text-gray-600 dark:text-gray-400">Missed</Text>
        </View>
      </View>
    </View>
  );
};

export default HabitProgressChart;

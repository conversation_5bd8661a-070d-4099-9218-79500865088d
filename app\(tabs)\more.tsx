import { Ionicons } from '@expo/vector-icons';
import { router, useFocusEffect } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Animated,
  Dimensions,
  Modal,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useResolvedTheme } from '../../src/components';
import { useAppStore } from '../../src/stores';

const { height: screenHeight } = Dimensions.get('window');

interface MoreMenuItem {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  route?: string;
  comingSoon?: boolean;
}

const moreMenuItems: MoreMenuItem[] = [
  {
    id: 'events',
    title: 'Events',
    description: 'Countdown to birthdays, anniversaries & holidays',
    icon: 'calendar-outline',
    route: '/events',
  },
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'Track your progress and insights',
    icon: 'analytics-outline',
    comingSoon: true,
  },
  {
    id: 'breathing',
    title: 'Breathing Exercises',
    description: 'Guided breathing and mindfulness',
    icon: 'leaf-outline',
    comingSoon: true,
  },
  {
    id: 'goals',
    title: 'Goals & Milestones',
    description: 'Set and track long-term objectives',
    icon: 'trophy-outline',
    comingSoon: true,
  },
  {
    id: 'backup',
    title: 'Backup & Sync',
    description: 'Secure your data across devices',
    icon: 'cloud-outline',
    comingSoon: true,
  },
];

export default function MoreScreen() {
  const { setActiveTab } = useAppStore();
  const [showDropdown, setShowDropdown] = useState(false);
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  const resolvedTheme = useResolvedTheme();

  useEffect(() => {
    setActiveTab('more');
  }, [setActiveTab]);

  // Auto-show dropdown when tab is focused
  useFocusEffect(
    useCallback(() => {
      setShowDropdown(true);
      return () => {
        // Cleanup when leaving the tab
        setShowDropdown(false);
      };
    }, [])
  );

  useEffect(() => {
    if (showDropdown) {
      // Animate dropdown in
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animate dropdown out
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenHeight,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [showDropdown, opacityAnim, slideAnim]);

  const handleMenuItemPress = (item: MoreMenuItem) => {
    setShowDropdown(false);

    if (item.comingSoon) {
      router.push('/coming-soon');
    } else if (item.route) {
      router.push(item.route as any);
    } else {
      // If no route specified, go back to Home
      router.push('/(tabs)/');
    }
  };

  const closeDropdown = () => {
    setShowDropdown(false);
    // Navigate back to Home tab when closing the dropdown
    router.push('/(tabs)/');
  };

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      <StatusBar style="auto" />

      {/* Header */}
      <View className="bg-white px-6 pb-6 pt-12 shadow-sm dark:bg-gray-800">
        <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">More</Text>
        <Text className="mt-1 text-gray-600 dark:text-gray-400">
          Explore additional features and tools
        </Text>
      </View>

      {/* Main Content - Hidden since dropdown shows automatically */}
      <View className="flex-1 items-center justify-center px-6 opacity-0">
        <View className="mb-8 items-center">
          <View className="mb-4 h-24 w-24 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
            <Ionicons
              name="ellipsis-horizontal"
              size={40}
              color={resolvedTheme === 'dark' ? '#3B82F6' : '#2563EB'}
            />
          </View>
          <Text className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
            Quick Access Menu
          </Text>
          <Text className="text-center text-gray-600 dark:text-gray-400">
            Menu will appear automatically
          </Text>
        </View>
      </View>

      {/* Dropdown Modal */}
      <Modal visible={showDropdown} transparent animationType="none" onRequestClose={closeDropdown}>
        <TouchableWithoutFeedback onPress={closeDropdown}>
          <View className="flex-1">
            <Animated.View
              style={{
                flex: 1,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                opacity: opacityAnim,
              }}
            />
            <Animated.View
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                transform: [{ translateY: slideAnim }],
              }}
              className="rounded-t-3xl bg-white shadow-2xl dark:bg-gray-800"
            >
              <View className="p-6">
                {/* Handle */}
                <View className="mb-6 h-1 w-12 self-center rounded-full bg-gray-300 dark:bg-gray-600" />

                {/* Menu Items */}
                <View className="space-y-2">
                  {moreMenuItems.map(item => (
                    <TouchableOpacity
                      key={item.id}
                      onPress={() => handleMenuItemPress(item)}
                      className="flex-row items-center rounded-xl bg-gray-50 p-4 dark:bg-gray-700"
                    >
                      <View className="mr-4 h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                        <Ionicons
                          name={item.icon}
                          size={24}
                          color={resolvedTheme === 'dark' ? '#3B82F6' : '#2563EB'}
                        />
                      </View>
                      <View className="flex-1">
                        <View className="flex-row items-center">
                          <Text className="font-semibold text-gray-900 dark:text-gray-100">
                            {item.title}
                          </Text>
                          {item.comingSoon && (
                            <View className="ml-2 rounded-full bg-orange-100 px-2 py-1 dark:bg-orange-900">
                              <Text className="text-xs font-medium text-orange-600 dark:text-orange-400">
                                Coming Soon
                              </Text>
                            </View>
                          )}
                        </View>
                        <Text className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                          {item.description}
                        </Text>
                      </View>
                      <Ionicons
                        name="chevron-forward"
                        size={20}
                        color={resolvedTheme === 'dark' ? '#9CA3AF' : '#6B7280'}
                      />
                    </TouchableOpacity>
                  ))}
                </View>

                {/* Close Button */}
                <TouchableOpacity
                  onPress={closeDropdown}
                  className="mt-6 rounded-xl bg-gray-200 py-4 dark:bg-gray-700"
                >
                  <Text className="text-center font-semibold text-gray-900 dark:text-gray-100">
                    Close
                  </Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
}

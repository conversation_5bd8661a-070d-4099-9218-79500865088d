import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export type TimerType = 'pomodoro';
export type PomodoroPhase = 'work' | 'break' | 'longBreak';
export type TimerStatus = 'idle' | 'running' | 'paused' | 'completed';

interface TimerState {
  // Current timer
  type: TimerType;
  status: TimerStatus;
  timeRemaining: number; // in seconds
  totalTime: number; // in seconds

  // Pomodoro specific
  pomodoroPhase: PomodoroPhase;
  pomodoroSession: number;
  pomodoroCompletedSessions: number;

  // Timer settings (from app store but cached here for performance)
  workDuration: number;
  breakDuration: number;
  longBreakDuration: number;
  sessionsUntilLongBreak: number;

  // Actions
  startTimer: (type: TimerType, duration?: number) => void;
  pauseTimer: () => void;
  resumeTimer: () => void;
  stopTimer: () => void;
  resetTimer: () => void;
  tick: () => void;
  setTimerType: (type: TimerType) => void;

  // Pomodoro specific actions
  startPomodoro: () => void;
  nextPomodoroPhase: () => void;
  resetPomodoro: () => void;

  // Settings
  updatePomodoroSettings: (settings: {
    workDuration?: number;
    breakDuration?: number;
    longBreakDuration?: number;
    sessionsUntilLongBreak?: number;
  }) => void;
}

export const useTimerStore = create<TimerState>()(
  devtools(
    (set, get) => ({
      // Initial state
      type: 'pomodoro',
      status: 'idle',
      timeRemaining: 25 * 60, // 25 minutes in seconds
      totalTime: 25 * 60,

      pomodoroPhase: 'work',
      pomodoroSession: 1,
      pomodoroCompletedSessions: 0,

      workDuration: 25,
      breakDuration: 5,
      longBreakDuration: 15,
      sessionsUntilLongBreak: 4,

      startTimer: (type, duration) => {
        const state = get();
        const timeInSeconds = state.workDuration * 60;

        set(
          {
            type: 'pomodoro',
            status: 'running',
            timeRemaining: timeInSeconds,
            totalTime: timeInSeconds,
            pomodoroPhase: 'work',
          },
          false,
          'startTimer'
        );
      },

      pauseTimer: () => {
        set({ status: 'paused' }, false, 'pauseTimer');
      },

      resumeTimer: () => {
        set({ status: 'running' }, false, 'resumeTimer');
      },

      stopTimer: () => {
        set({ status: 'idle' }, false, 'stopTimer');
      },

      resetTimer: () => {
        const state = get();
        let timeInSeconds: number;

        switch (state.pomodoroPhase) {
          case 'work':
            timeInSeconds = state.workDuration * 60;
            break;
          case 'break':
            timeInSeconds = state.breakDuration * 60;
            break;
          case 'longBreak':
            timeInSeconds = state.longBreakDuration * 60;
            break;
        }

        set(
          {
            status: 'idle',
            timeRemaining: timeInSeconds,
            totalTime: timeInSeconds,
          },
          false,
          'resetTimer'
        );
      },

      setTimerType: type => {
        const state = get();

        // Only allow changing type when timer is idle
        if (state.status !== 'idle') return;

        const timeInSeconds = state.workDuration * 60;

        set(
          {
            type: 'pomodoro',
            timeRemaining: timeInSeconds,
            totalTime: timeInSeconds,
            pomodoroPhase: 'work',
          },
          false,
          'setTimerType'
        );
      },

      tick: () => {
        const state = get();

        if (state.status !== 'running') return;

        const newTimeRemaining = Math.max(0, state.timeRemaining - 1);

        if (newTimeRemaining === 0) {
          set(
            {
              timeRemaining: 0,
              status: 'completed',
            },
            false,
            'tick/completed'
          );

          // Auto-advance Pomodoro phases
          setTimeout(() => get().nextPomodoroPhase(), 1000);
        } else {
          set(
            {
              timeRemaining: newTimeRemaining,
            },
            false,
            'tick'
          );
        }
      },

      startPomodoro: () => {
        const state = get();
        const timeInSeconds = state.workDuration * 60;

        set(
          {
            type: 'pomodoro',
            status: 'running',
            timeRemaining: timeInSeconds,
            totalTime: timeInSeconds,
            pomodoroPhase: 'work',
            pomodoroSession: 1,
            pomodoroCompletedSessions: 0,
          },
          false,
          'startPomodoro'
        );
      },

      nextPomodoroPhase: () => {
        const state = get();
        let nextPhase: PomodoroPhase;
        let nextSession = state.pomodoroSession;
        let completedSessions = state.pomodoroCompletedSessions;
        let timeInSeconds: number;

        if (state.pomodoroPhase === 'work') {
          completedSessions += 1;

          if (completedSessions % state.sessionsUntilLongBreak === 0) {
            nextPhase = 'longBreak';
            timeInSeconds = state.longBreakDuration * 60;
          } else {
            nextPhase = 'break';
            timeInSeconds = state.breakDuration * 60;
          }
        } else {
          nextPhase = 'work';
          nextSession += 1;
          timeInSeconds = state.workDuration * 60;
        }

        set(
          {
            pomodoroPhase: nextPhase,
            pomodoroSession: nextSession,
            pomodoroCompletedSessions: completedSessions,
            timeRemaining: timeInSeconds,
            totalTime: timeInSeconds,
            status: 'idle', // Let user manually start next phase
          },
          false,
          'nextPomodoroPhase'
        );
      },

      resetPomodoro: () => {
        const state = get();
        const timeInSeconds = state.workDuration * 60;

        set(
          {
            status: 'idle',
            timeRemaining: timeInSeconds,
            totalTime: timeInSeconds,
            pomodoroPhase: 'work',
            pomodoroSession: 1,
            pomodoroCompletedSessions: 0,
          },
          false,
          'resetPomodoro'
        );
      },

      updatePomodoroSettings: settings => {
        const state = get();
        const newState = {
          workDuration: settings.workDuration ?? state.workDuration,
          breakDuration: settings.breakDuration ?? state.breakDuration,
          longBreakDuration: settings.longBreakDuration ?? state.longBreakDuration,
          sessionsUntilLongBreak: settings.sessionsUntilLongBreak ?? state.sessionsUntilLongBreak,
        };

        // Update time remaining if timer is idle and in work phase
        let timeRemaining = state.timeRemaining;
        let totalTime = state.totalTime;

        if (state.status === 'idle' && state.pomodoroPhase === 'work') {
          timeRemaining = newState.workDuration * 60;
          totalTime = newState.workDuration * 60;
        }

        set(
          {
            ...newState,
            timeRemaining,
            totalTime,
          },
          false,
          'updatePomodoroSettings'
        );
      },
    }),
    {
      name: 'timer-store',
    }
  )
);

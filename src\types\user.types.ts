/**
 * Type definitions for user-related data structures
 */

export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'es' | 'fr' | 'de' | 'zh' | 'ja';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface UserSettings {
  theme: Theme;
  language: Language;
  notifications_enabled: boolean;
  reminder_notifications_enabled: boolean;
  sound_enabled: boolean;
  vibration_enabled: boolean;
  weekly_report_enabled: boolean;
  monthly_report_enabled: boolean;
}

export interface UserPreferences {
  default_habit_color: string;
  default_habit_category: string;
  default_habit_frequency: string;
  default_habit_unit: string;
  default_reminder_time: string;
  show_streak_animations: boolean;
  show_completion_celebrations: boolean;
  compact_view_enabled: boolean;
}

export interface UserStats {
  total_habits_created: number;
  total_completions: number;
  current_active_habits: number;
  longest_overall_streak: number;
  days_since_signup: number;
  completion_rate_all_time: number;
  completion_rate_this_month: number;
  completion_rate_this_week: number;
  most_productive_day_of_week: string;
  most_productive_time_of_day: string;
}

export interface UserData extends UserProfile {
  settings: UserSettings;
  preferences: UserPreferences;
  stats: UserStats;
}

export interface UserFormData {
  name: string;
  email: string;
  avatar_url?: string;
}

export interface UserSettingsFormData {
  theme: Theme;
  language: Language;
  notifications_enabled: boolean;
  reminder_notifications_enabled: boolean;
  sound_enabled: boolean;
  vibration_enabled: boolean;
  weekly_report_enabled: boolean;
  monthly_report_enabled: boolean;
}

export interface UserPreferencesFormData {
  default_habit_color: string;
  default_habit_category: string;
  default_habit_frequency: string;
  default_habit_unit: string;
  default_reminder_time: string;
  show_streak_animations: boolean;
  show_completion_celebrations: boolean;
  compact_view_enabled: boolean;
}

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  email_verified: boolean;
  created_at: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface UserActivity {
  id: string;
  user_id: string;
  activity_type: 'habit_created' | 'habit_completed' | 'streak_achieved' | 'goal_reached';
  activity_data: Record<string, any>;
  created_at: string;
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_type: string;
  achievement_name: string;
  achievement_description: string;
  icon: string;
  earned_at: string;
  progress?: number;
  target?: number;
}

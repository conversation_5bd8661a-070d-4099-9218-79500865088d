import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { databaseService } from '../database';
import { User } from '../database/entities';

interface UserState {
  currentUser: User | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  setCurrentUser: (user: User | null) => void;
  loadUser: (userId?: string) => Promise<void>;
  updateUser: (userId: string, userData: Partial<User>) => Promise<void>;
  createUser: (userData: Partial<User>) => Promise<void>;
  clearError: () => void;
  logout: () => void;
}

export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        currentUser: null,
        isLoading: false,
        error: null,

        setCurrentUser: user => {
          set({ currentUser: user }, false, 'setCurrentUser');
        },

        loadUser: async userId => {
          set({ isLoading: true, error: null }, false, 'loadUser/start');

          try {
            let user: User | null = null;

            if (userId) {
              user = await databaseService.userRepository.findById(userId);
            } else {
              // Load default user if no userId provided
              const users = await databaseService.userRepository.findAll();
              user = users.length > 0 ? users[0] : null;
            }

            set(
              {
                currentUser: user,
                isLoading: false,
              },
              false,
              'loadUser/success'
            );
          } catch (error) {
            set(
              {
                error: error instanceof Error ? error.message : 'Failed to load user',
                isLoading: false,
              },
              false,
              'loadUser/error'
            );
          }
        },

        updateUser: async (userId, userData) => {
          set({ isLoading: true, error: null }, false, 'updateUser/start');

          try {
            const updatedUser = await databaseService.userRepository.update(userId, userData);

            if (updatedUser) {
              set(
                {
                  currentUser: updatedUser,
                  isLoading: false,
                },
                false,
                'updateUser/success'
              );
            }
          } catch (error) {
            set(
              {
                error: error instanceof Error ? error.message : 'Failed to update user',
                isLoading: false,
              },
              false,
              'updateUser/error'
            );
          }
        },

        createUser: async userData => {
          set({ isLoading: true, error: null }, false, 'createUser/start');

          try {
            const newUser = await databaseService.userRepository.create(userData);

            set(
              {
                currentUser: newUser,
                isLoading: false,
              },
              false,
              'createUser/success'
            );
          } catch (error) {
            set(
              {
                error: error instanceof Error ? error.message : 'Failed to create user',
                isLoading: false,
              },
              false,
              'createUser/error'
            );
          }
        },

        clearError: () => {
          set({ error: null }, false, 'clearError');
        },

        logout: () => {
          set(
            {
              currentUser: null,
              error: null,
            },
            false,
            'logout'
          );
        },
      }),
      {
        name: 'user-store',
        storage: createJSONStorage(() => AsyncStorage),
        partialize: state => ({
          currentUser: state.currentUser,
        }),
      }
    ),
    {
      name: 'user-store',
    }
  )
);

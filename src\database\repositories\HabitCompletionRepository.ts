import { Between, Repository } from 'typeorm';
import { AppDataSource } from '../config';
import { HabitCompletion } from '../entities';

export class HabitCompletionRepository {
  private repository: Repository<HabitCompletion>;

  constructor() {
    this.repository = AppDataSource.getRepository(HabitCompletion);
  }

  async create(completionData: Partial<HabitCompletion>): Promise<HabitCompletion> {
    // Import UUID generator locally to avoid auto-formatting removal
    const { generateUUID } = await import('../../utils/uuidUtils');

    const completion = this.repository.create({
      ...completionData,
      id: generateUUID(), // Manually set UUID to avoid crypto.getRandomValues() issue
    });
    return await this.repository.save(completion);
  }

  async findById(id: string): Promise<HabitCompletion | null> {
    return await this.repository.findOne({
      where: { id },
      relations: ['habit'],
    });
  }

  async findByHabitAndDate(habitId: string, date: string): Promise<HabitCompletion | null> {
    return await this.repository.findOne({
      where: { habit_id: habitId, completed_date: date },
    });
  }

  async findByHabitId(habitId: string): Promise<HabitCompletion[]> {
    return await this.repository.find({
      where: { habit_id: habitId },
      order: { completed_date: 'DESC' },
    });
  }

  async findByDateRange(
    habitId: string,
    startDate: string,
    endDate: string
  ): Promise<HabitCompletion[]> {
    return await this.repository.find({
      where: {
        habit_id: habitId,
        completed_date: Between(startDate, endDate),
      },
      order: { completed_date: 'ASC' },
    });
  }

  async update(
    id: string,
    completionData: Partial<HabitCompletion>
  ): Promise<HabitCompletion | null> {
    await this.repository.update(id, completionData);
    return await this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== 0;
  }

  async deleteByHabitAndDate(habitId: string, date: string): Promise<boolean> {
    const result = await this.repository.delete({
      habit_id: habitId,
      completed_date: date,
    });
    return result.affected !== 0;
  }

  async getCompletionStats(
    habitId: string,
    days: number = 30
  ): Promise<{
    totalCompletions: number;
    averageCount: number;
    completionRate: number;
  }> {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const completions = await this.findByDateRange(habitId, startDate, endDate);
    const totalCompletions = completions.length;
    const totalCount = completions.reduce((sum, completion) => sum + completion.count, 0);
    const averageCount = totalCompletions > 0 ? totalCount / totalCompletions : 0;
    const completionRate = (totalCompletions / days) * 100;

    return {
      totalCompletions,
      averageCount,
      completionRate,
    };
  }
}

// https://docs.expo.dev/guides/using-eslint/
const { defineConfig } = require('eslint/config');
const expoConfig = require('eslint-config-expo/flat');

module.exports = defineConfig([
  expoConfig,
  {
    ignores: [
      'dist/*',
      'build/*',
      '.expo/*',
      'node_modules/*',
      '*.generated.*',
      'metro.config.js',
      'babel.config.js',
    ],
  },
  {
    rules: {
      // General React rules
      'react/prop-types': 'off', // We use TypeScript
      'react/react-in-jsx-scope': 'off', // Not needed in React 17+

      // General code quality
      'no-console': 'warn',
      'prefer-const': 'warn',
      'no-var': 'error',
    },
  },
]);

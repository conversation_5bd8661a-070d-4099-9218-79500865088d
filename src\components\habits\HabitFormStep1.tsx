import React from 'react';
import { Text, TextInput, View } from 'react-native';
import { HabitFormData } from '../../types/habit.types';
import HabitCategoryPicker from './HabitCategoryPicker';
import HabitColorPicker from './HabitColorPicker';

interface HabitFormStep1Props {
  formData: HabitFormData;
  errors: Partial<Record<keyof HabitFormData, string>>;
  updateFormData: <K extends keyof HabitFormData>(key: K, value: HabitFormData[K]) => void;
}

const HabitFormStep1: React.FC<HabitFormStep1Props> = ({ formData, errors, updateFormData }) => {
  return (
    <View className="space-y-6">
      {/* Step Title */}
      <View className="mb-6 text-center">
        <Text className="mb-2 text-2xl font-bold text-gray-900 dark:text-gray-100">
          Basic Information
        </Text>
        <Text className="text-gray-600 dark:text-gray-400">
          Let&apos;s start with the basics. What habit do you want to build?
        </Text>
      </View>

      {/* Habit Name */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Habit Name *
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          Choose a clear, specific name for your habit
        </Text>
        <TextInput
          value={formData.name}
          onChangeText={text => updateFormData('name', text)}
          placeholder="e.g., Drink 8 glasses of water"
          placeholderTextColor="#9CA3AF"
          className={`rounded-xl border-2 bg-white px-4 py-3 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ${
            errors.name ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'
          }`}
          maxLength={100}
        />
        {errors.name && <Text className="mt-1 text-sm text-red-500">{errors.name}</Text>}
      </View>

      {/* Purpose */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Purpose *
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          Why do you want to build this habit? What will it help you achieve?
        </Text>
        <TextInput
          value={formData.purpose}
          onChangeText={text => updateFormData('purpose', text)}
          placeholder="e.g., To improve my health and energy levels, to become more disciplined..."
          placeholderTextColor="#9CA3AF"
          multiline
          numberOfLines={4}
          className={`rounded-xl border-2 bg-white px-4 py-3 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ${
            errors.purpose ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'
          }`}
          style={{ textAlignVertical: 'top' }}
          maxLength={300}
        />
        {errors.purpose && <Text className="mt-1 text-sm text-red-500">{errors.purpose}</Text>}
        <Text className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {formData.purpose.length}/300 characters
        </Text>
      </View>

      {/* Description (Optional) */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Description (Optional)
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          Add any additional details about your habit
        </Text>
        <TextInput
          value={formData.description || ''}
          onChangeText={text => updateFormData('description', text)}
          placeholder="e.g., Start with one glass when I wake up, then one before each meal..."
          placeholderTextColor="#9CA3AF"
          multiline
          numberOfLines={3}
          className={`rounded-xl border-2 bg-white px-4 py-3 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ${
            errors.description ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'
          }`}
          style={{ textAlignVertical: 'top' }}
          maxLength={500}
        />
        {errors.description && (
          <Text className="mt-1 text-sm text-red-500">{errors.description}</Text>
        )}
        <Text className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {(formData.description || '').length}/500 characters
        </Text>
      </View>

      {/* Category */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Category
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          Choose a category that best describes your habit
        </Text>
        <HabitCategoryPicker
          selectedCategory={formData.category}
          onCategorySelect={category => updateFormData('category', category)}
        />
      </View>

      {/* Color */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Color Theme
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          Pick a color to help identify your habit
        </Text>
        <HabitColorPicker
          selectedColor={formData.color}
          onColorSelect={color => updateFormData('color', color)}
        />
      </View>

      {/* Progress Indicator */}
      <View className="mt-6 rounded-xl bg-blue-50 p-4 dark:bg-blue-900">
        <View className="flex-row items-center">
          <View className="mr-3 h-3 w-3 rounded-full bg-blue-500" />
          <Text className="font-medium text-blue-700 dark:text-blue-300">
            Step 1 of 4: Basic Information
          </Text>
        </View>
        <Text className="mt-2 text-sm text-blue-600 dark:text-blue-400">
          Great start! Next, we&apos;ll set up your habit schedule and frequency.
        </Text>
      </View>
    </View>
  );
};

export default HabitFormStep1;

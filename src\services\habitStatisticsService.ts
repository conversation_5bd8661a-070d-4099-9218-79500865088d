import { databaseService } from '../database';
import { HabitAnalytics, HabitStats } from '../types/habit.types';

export class HabitStatisticsService {
  /**
   * Calculate comprehensive statistics for a single habit
   */
  async calculateHabitStats(habitId: string): Promise<HabitStats> {
    const completions = await databaseService.habitCompletionRepository.findByHabitId(habitId);
    const currentStreak =
      await databaseService.habitStreakRepository.getCurrentStreakLength(habitId);
    const longestStreak = await databaseService.habitStreakRepository.getLongestStreak(habitId);

    const totalCompletions = completions.length;
    const lastCompletedDate =
      completions.length > 0
        ? completions.sort(
            (a, b) => new Date(b.completed_date).getTime() - new Date(a.completed_date).getTime()
          )[0].completed_date
        : undefined;

    // Calculate completion rate (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentCompletions = completions.filter(
      completion => new Date(completion.completed_date) >= thirtyDaysAgo
    );
    const completionRate = recentCompletions.length / 30; // completions per day

    // Calculate weekly and monthly averages
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const weeklyCompletions = completions.filter(
      completion => new Date(completion.completed_date) >= oneWeekAgo
    ).length;

    const monthlyCompletions = completions.filter(
      completion => new Date(completion.completed_date) >= oneMonthAgo
    ).length;

    return {
      totalCompletions,
      currentStreak,
      longestStreak,
      completionRate: Math.round(completionRate * 100) / 100,
      averageCompletionsPerWeek: weeklyCompletions,
      averageCompletionsPerMonth: monthlyCompletions,
      lastCompletedDate,
    };
  }

  /**
   * Calculate analytics for all user habits
   */
  async calculateUserAnalytics(userId: string): Promise<HabitAnalytics> {
    const habits = await databaseService.habitRepository.findByUserId(userId);
    const activeHabits = habits.filter(habit => habit.is_active);

    const today = new Date().toISOString().split('T')[0];
    let completedToday = 0;
    let totalTarget = 0;
    let mostCompletedHabit: { id: string; name: string; completions: number } | undefined;
    let longestStreakHabit: { id: string; name: string; streak: number } | undefined;
    let maxCompletions = 0;
    let maxStreak = 0;

    const categoryBreakdown: Record<string, number> = {};
    const weeklyProgress: { date: string; completions: number; target: number }[] = [];
    const monthlyProgress: { month: string; completions: number; target: number }[] = [];

    // Calculate stats for each habit
    for (const habit of activeHabits) {
      const completions = await databaseService.habitCompletionRepository.findByHabitId(habit.id);
      const currentStreak = await databaseService.habitStreakRepository.getCurrentStreakLength(
        habit.id
      );

      // Check if completed today
      const completedToday_habit = completions.some(c => c.completed_date === today);
      if (completedToday_habit) {
        completedToday++;
      }
      totalTarget++;

      // Track most completed habit
      if (completions.length > maxCompletions) {
        maxCompletions = completions.length;
        mostCompletedHabit = {
          id: habit.id,
          name: habit.name,
          completions: completions.length,
        };
      }

      // Track longest streak habit
      if (currentStreak > maxStreak) {
        maxStreak = currentStreak;
        longestStreakHabit = {
          id: habit.id,
          name: habit.name,
          streak: currentStreak,
        };
      }

      // Category breakdown
      categoryBreakdown[habit.category] = (categoryBreakdown[habit.category] || 0) + 1;
    }

    // Calculate category percentages
    const categoryBreakdownArray = Object.entries(categoryBreakdown).map(([category, count]) => ({
      category: category as any,
      count,
      percentage: Math.round((count / activeHabits.length) * 100),
    }));

    // Calculate weekly progress (last 7 days)
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      let dayCompletions = 0;
      for (const habit of activeHabits) {
        const completion = await databaseService.habitCompletionRepository.findByHabitAndDate(
          habit.id,
          dateStr
        );
        if (completion) {
          dayCompletions++;
        }
      }

      weeklyProgress.push({
        date: dateStr,
        completions: dayCompletions,
        target: activeHabits.length,
      });
    }

    // Calculate monthly progress (last 12 months)
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthStr = date.toISOString().substring(0, 7); // YYYY-MM format

      let monthCompletions = 0;
      for (const habit of activeHabits) {
        const startDate = `${monthStr}-01`;
        const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
          .toISOString()
          .split('T')[0];
        const completions = await databaseService.habitCompletionRepository.findByDateRange(
          habit.id,
          startDate,
          endDate
        );
        monthCompletions += completions.length;
      }

      monthlyProgress.push({
        month: monthStr,
        completions: monthCompletions,
        target:
          activeHabits.length * new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate(), // days in month
      });
    }

    // Calculate average streak length
    let totalStreaks = 0;
    for (const habit of activeHabits) {
      const currentStreak = await databaseService.habitStreakRepository.getCurrentStreakLength(
        habit.id
      );
      totalStreaks += currentStreak;
    }
    const averageStreakLength =
      activeHabits.length > 0 ? Math.round(totalStreaks / activeHabits.length) : 0;

    return {
      totalHabits: habits.length,
      activeHabits: activeHabits.length,
      completedToday,
      completionRateToday: totalTarget > 0 ? Math.round((completedToday / totalTarget) * 100) : 0,
      averageStreakLength,
      mostCompletedHabit,
      longestStreakHabit,
      categoryBreakdown: categoryBreakdownArray,
      weeklyProgress,
      monthlyProgress,
    };
  }

  /**
   * Get completion history for a habit over a date range
   */
  async getCompletionHistory(
    habitId: string,
    days: number = 30
  ): Promise<{ date: string; completed: boolean; count: number }[]> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const completions = await databaseService.habitCompletionRepository.findByDateRange(
      habitId,
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    );

    const history: { date: string; completed: boolean; count: number }[] = [];

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      const completion = completions.find(c => c.completed_date === dateStr);
      history.push({
        date: dateStr,
        completed: !!completion,
        count: completion?.count || 0,
      });
    }

    return history;
  }

  /**
   * Calculate streak information for a habit
   */
  async getStreakInfo(habitId: string): Promise<{
    current: number;
    longest: number;
    streaks: { start: string; end: string; length: number }[];
  }> {
    const currentStreak =
      await databaseService.habitStreakRepository.getCurrentStreakLength(habitId);
    const longestStreak = await databaseService.habitStreakRepository.getLongestStreak(habitId);
    const allStreaks = await databaseService.habitStreakRepository.findByHabitId(habitId);

    const streaks = allStreaks.map(streak => ({
      start: streak.start_date,
      end: streak.end_date || new Date().toISOString().split('T')[0],
      length: streak.length,
    }));

    return {
      current: currentStreak,
      longest: longestStreak,
      streaks,
    };
  }
}

export const habitStatisticsService = new HabitStatisticsService();

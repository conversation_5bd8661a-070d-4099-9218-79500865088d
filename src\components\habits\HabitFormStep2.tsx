import React, { useState } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import { HabitFormData, HabitFrequency, HabitUnit } from '../../types/habit.types';
import HabitFrequencyPicker from './HabitFrequencyPicker';
import HabitTimeRangePicker from './HabitTimeRangePicker';

interface HabitFormStep2Props {
  formData: HabitFormData;
  errors: Partial<Record<keyof HabitFormData, string>>;
  updateFormData: <K extends keyof HabitFormData>(key: K, value: HabitFormData[K]) => void;
}

const HABIT_UNITS: { value: HabitUnit; label: string }[] = [
  { value: 'times', label: 'times' },
  { value: 'minutes', label: 'minutes' },
  { value: 'hours', label: 'hours' },
  { value: 'pages', label: 'pages' },
  { value: 'glasses', label: 'glasses' },
  { value: 'steps', label: 'steps' },
  { value: 'kilometers', label: 'kilometers' },
  { value: 'miles', label: 'miles' },
  { value: 'pounds', label: 'pounds' },
  { value: 'kilograms', label: 'kilograms' },
  { value: 'other', label: 'other' },
];

const HabitFormStep2: React.FC<HabitFormStep2Props> = ({ formData, errors, updateFormData }) => {
  const [showUnitPicker, setShowUnitPicker] = useState(false);

  return (
    <View className="space-y-6">
      {/* Step Title */}
      <View className="mb-6 text-center">
        <Text className="mb-2 text-2xl font-bold text-gray-900 dark:text-gray-100">
          Schedule & Frequency
        </Text>
        <Text className="text-gray-600 dark:text-gray-400">
          When and how often do you want to practice this habit?
        </Text>
      </View>

      {/* Frequency */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Frequency *
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          How often do you want to practice this habit?
        </Text>
        <HabitFrequencyPicker
          selectedFrequency={formData.frequency}
          onFrequencySelect={(frequency: HabitFrequency) => updateFormData('frequency', frequency)}
        />
      </View>

      {/* Target Count and Unit */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Target Goal *
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          How much do you want to do each time?
        </Text>
        <View className="flex-row space-x-3">
          <View className="flex-1">
            <TextInput
              value={formData.target_count.toString()}
              onChangeText={text => {
                const count = parseInt(text) || 1;
                updateFormData('target_count', Math.max(1, Math.min(100, count)));
              }}
              placeholder="1"
              placeholderTextColor="#9CA3AF"
              keyboardType="numeric"
              className={`rounded-xl border-2 bg-white px-4 py-3 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ${
                errors.target_count ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'
              }`}
            />
          </View>
          <TouchableOpacity
            onPress={() => setShowUnitPicker(!showUnitPicker)}
            className="flex-2 rounded-xl border-2 border-gray-200 bg-white px-4 py-3 dark:border-gray-700 dark:bg-gray-800"
          >
            <Text className="text-gray-900 dark:text-gray-100">
              {HABIT_UNITS.find(unit => unit.value === formData.unit)?.label || 'times'}
            </Text>
          </TouchableOpacity>
        </View>
        {errors.target_count && (
          <Text className="mt-1 text-sm text-red-500">{errors.target_count}</Text>
        )}

        {/* Unit Picker */}
        {showUnitPicker && (
          <View className="mt-3 rounded-xl border border-gray-200 bg-white p-2 dark:border-gray-700 dark:bg-gray-800">
            <View className="flex-row flex-wrap">
              {HABIT_UNITS.map(unit => (
                <TouchableOpacity
                  key={unit.value}
                  onPress={() => {
                    updateFormData('unit', unit.value);
                    setShowUnitPicker(false);
                  }}
                  className={`m-1 rounded-lg px-3 py-2 ${
                    formData.unit === unit.value ? 'bg-blue-500' : 'bg-gray-100 dark:bg-gray-700'
                  }`}
                >
                  <Text
                    className={`text-sm ${
                      formData.unit === unit.value
                        ? 'text-white'
                        : 'text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {unit.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </View>

      {/* Time Range */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Preferred Time Range (Optional)
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          When do you prefer to practice this habit?
        </Text>
        <HabitTimeRangePicker
          timeRange={formData.timeRange}
          onTimeRangeChange={timeRange => updateFormData('timeRange', timeRange)}
        />
      </View>

      {/* Reminders */}
      <View>
        <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
          Reminders (Optional)
        </Text>
        <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          Get notified when it&apos;s time to practice your habit
        </Text>

        <TouchableOpacity
          onPress={() => updateFormData('reminder_enabled', !formData.reminder_enabled)}
          className="mb-3 flex-row items-center"
        >
          <View
            className={`mr-3 h-6 w-6 items-center justify-center rounded border-2 ${
              formData.reminder_enabled
                ? 'border-blue-500 bg-blue-500'
                : 'border-gray-300 dark:border-gray-600'
            }`}
          >
            {formData.reminder_enabled && <Text className="text-xs text-white">✓</Text>}
          </View>
          <Text className="text-gray-900 dark:text-gray-100">Enable reminder notifications</Text>
        </TouchableOpacity>

        {formData.reminder_enabled && (
          <TextInput
            value={formData.reminder_time || ''}
            onChangeText={text => updateFormData('reminder_time', text)}
            placeholder="HH:MM (e.g., 09:00)"
            placeholderTextColor="#9CA3AF"
            className="rounded-xl border-2 border-gray-200 bg-white px-4 py-3 text-gray-900 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
          />
        )}
      </View>

      {/* Progress Indicator */}
      <View className="mt-6 rounded-xl bg-blue-50 p-4 dark:bg-blue-900">
        <View className="flex-row items-center">
          <View className="mr-3 h-3 w-3 rounded-full bg-blue-500" />
          <Text className="font-medium text-blue-700 dark:text-blue-300">
            Step 2 of 4: Schedule & Frequency
          </Text>
        </View>
        <Text className="mt-2 text-sm text-blue-600 dark:text-blue-400">
          Perfect! Now let&apos;s break down your habit into manageable sub-tasks.
        </Text>
      </View>
    </View>
  );
};

export default HabitFormStep2;

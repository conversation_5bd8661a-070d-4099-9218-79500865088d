import { Repository } from 'typeorm';
import { AppDataSource } from '../config';
import { Habit } from '../entities';

export class HabitRepository {
  private repository: Repository<Habit>;

  constructor() {
    this.repository = AppDataSource.getRepository(Habit);
  }

  async create(habitData: Partial<Habit>): Promise<Habit> {
    // Import UUID generator locally to avoid auto-formatting removal
    const { generateUUID } = await import('../../utils/uuidUtils');

    const habit = this.repository.create({
      ...habitData,
      id: generateUUID(), // Manually set UUID to avoid crypto.getRandomValues() issue
    });
    return await this.repository.save(habit);
  }

  async findById(id: string): Promise<Habit | null> {
    return await this.repository.findOne({
      where: { id },
      relations: ['user', 'completions', 'streaks'],
    });
  }

  async findByUserId(userId: string): Promise<Habit[]> {
    return await this.repository.find({
      where: { user_id: userId, is_active: true },
      relations: ['completions', 'streaks'],
      order: { sort_order: 'ASC', created_at: 'DESC' },
    });
  }

  async update(id: string, habitData: Partial<Habit>): Promise<Habit | null> {
    await this.repository.update(id, habitData);
    return await this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== 0;
  }

  async softDelete(id: string): Promise<boolean> {
    const result = await this.repository.update(id, { is_active: false });
    return result.affected !== 0;
  }

  async updateSortOrder(habitId: string, newOrder: number): Promise<void> {
    await this.repository.update(habitId, { sort_order: newOrder });
  }

  async findActiveHabits(userId: string): Promise<Habit[]> {
    return await this.repository.find({
      where: { user_id: userId, is_active: true },
      relations: ['completions', 'streaks'],
      order: { sort_order: 'ASC' },
    });
  }
}

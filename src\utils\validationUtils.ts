/**
 * Validation utility functions for the WinArc app
 */

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPassword = (password: string): boolean => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

export const isValidName = (name: string): boolean => {
  return name.trim().length >= 2 && name.trim().length <= 50;
};

export const isValidHabitName = (name: string): boolean => {
  return name.trim().length >= 1 && name.trim().length <= 100;
};

export const isValidDescription = (description: string): boolean => {
  return description.length <= 500;
};

export const isValidTargetCount = (count: number): boolean => {
  return count > 0 && count <= 1000 && Number.isInteger(count);
};

export const isValidColor = (color: string): boolean => {
  // Check if it's a valid hex color
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexRegex.test(color);
};

export const isValidFrequency = (frequency: string): boolean => {
  const validFrequencies = ['daily', 'weekly', 'monthly'];
  return validFrequencies.includes(frequency.toLowerCase());
};

export const isValidCategory = (category: string): boolean => {
  const validCategories = [
    'health',
    'fitness',
    'productivity',
    'learning',
    'mindfulness',
    'social',
    'creativity',
    'finance',
    'other',
  ];
  return validCategories.includes(category.toLowerCase());
};

export const isValidUnit = (unit: string): boolean => {
  const validUnits = [
    'times',
    'minutes',
    'hours',
    'pages',
    'glasses',
    'steps',
    'kilometers',
    'miles',
    'pounds',
    'kilograms',
    'other',
  ];
  return validUnits.includes(unit.toLowerCase());
};

export const isValidReminderTime = (time: string): boolean => {
  // Check if time is in HH:MM format
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
};

export const isValidMoodRating = (rating: number): boolean => {
  return rating >= 1 && rating <= 5 && Number.isInteger(rating);
};

export const isValidNotes = (notes: string): boolean => {
  return notes.length <= 1000;
};

export const isValidTheme = (theme: string): boolean => {
  const validThemes = ['light', 'dark', 'system'];
  return validThemes.includes(theme);
};

export const isValidLanguage = (language: string): boolean => {
  const validLanguages = ['en', 'es', 'fr', 'de', 'zh', 'ja'];
  return validLanguages.includes(language);
};

export const isValidTimerDuration = (minutes: number): boolean => {
  return minutes > 0 && minutes <= 180; // Max 3 hours
};

export const validateHabitData = (data: {
  name: string;
  description?: string;
  category: string;
  frequency: string;
  target_count: number;
  unit: string;
  color: string;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!isValidHabitName(data.name)) {
    errors.push('Habit name must be between 1 and 100 characters');
  }

  if (data.description && !isValidDescription(data.description)) {
    errors.push('Description must be less than 500 characters');
  }

  if (!isValidCategory(data.category)) {
    errors.push('Invalid category selected');
  }

  if (!isValidFrequency(data.frequency)) {
    errors.push('Invalid frequency selected');
  }

  if (!isValidTargetCount(data.target_count)) {
    errors.push('Target count must be a positive integer between 1 and 1000');
  }

  if (!isValidUnit(data.unit)) {
    errors.push('Invalid unit selected');
  }

  if (!isValidColor(data.color)) {
    errors.push('Invalid color format');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateUserData = (data: {
  name: string;
  email: string;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!isValidName(data.name)) {
    errors.push('Name must be between 2 and 50 characters');
  }

  if (!isValidEmail(data.email)) {
    errors.push('Please enter a valid email address');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

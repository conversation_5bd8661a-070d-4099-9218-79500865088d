import { Repository } from 'typeorm';
import { AppDataSource } from '../config';
import { User } from '../entities';

export class UserRepository {
  private repository: Repository<User>;

  constructor() {
    this.repository = AppDataSource.getRepository(User);
  }

  async create(userData: Partial<User>): Promise<User> {
    // Import UUID generator locally to avoid auto-formatting removal
    const { generateUUID } = await import('../../utils/uuidUtils');

    const user = this.repository.create({
      ...userData,
      id: generateUUID(), // Manually set UUID to avoid crypto.getRandomValues() issue
    });
    return await this.repository.save(user);
  }

  async findById(id: string): Promise<User | null> {
    return await this.repository.findOne({
      where: { id },
      relations: ['habits'],
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.repository.findOne({
      where: { email },
    });
  }

  async update(id: string, userData: Partial<User>): Promise<User | null> {
    await this.repository.update(id, userData);
    return await this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== 0;
  }

  async findAll(): Promise<User[]> {
    return await this.repository.find({
      relations: ['habits'],
    });
  }
}

/**
 * Global Test Setup
 * Runs once before all tests
 */

module.exports = async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.EXPO_PUBLIC_ENV = 'test';

  // Suppress console warnings during tests
  // eslint-disable-next-line no-console
  const originalWarn = console.warn;
  console.warn = (...args) => {
    // Suppress specific React Native warnings that are not relevant in tests
    if (
      args[0] &&
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: React.createElement') ||
        args[0].includes('Warning: componentWillReceiveProps') ||
        args[0].includes('Warning: componentWillMount'))
    ) {
      return;
    }
    originalWarn.apply(console, args);
  };

  // Global test configuration
  global.__TEST_ENV__ = true;

  // Global test setup completed
};

import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('habit_subtask_completions')
@Index(['habit_id', 'subtask_id', 'completed_date'], { unique: true })
export class HabitSubTaskCompletion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  habit_id: string;

  @Column({ type: 'varchar', length: 255 })
  subtask_id: string; // References the sub-task ID from the habit's sub_tasks JSON

  @Column({ type: 'date' })
  completed_date: string; // Format: YYYY-MM-DD

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @CreateDateColumn()
  created_at: Date;

  @ManyToOne('Habit', 'completions', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'habit_id' })
  habit: any;
}

import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Alert, Modal, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { useResolvedTheme } from '../src/components';
import { useAppStore } from '../src/stores';
import { useEventStore } from '../src/stores/eventStore';

export default function EventsScreen() {
  const { setActiveTab } = useAppStore();
  const {
    upcomingEvents,
    isLoading: eventsLoading,
    fetchUpcomingEvents,
    createEvent,
    deleteEvent,
  } = useEventStore();

  const [showAddModal, setShowAddModal] = useState(false);
  const [newEvent, setNewEvent] = useState({
    name: '',
    date: '',
    type: 'birthday' as 'birthday' | 'anniversary' | 'holiday',
  });

  const resolvedTheme = useResolvedTheme();

  useEffect(() => {
    setActiveTab('events');
    // Fetch upcoming events for the default user
    fetchUpcomingEvents('default-user', 10);
  }, [fetchUpcomingEvents, setActiveTab]);

  const handleAddEvent = async () => {
    if (!newEvent.name.trim() || !newEvent.date.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      await createEvent({
        name: newEvent.name.trim(),
        date: new Date(newEvent.date),
        type: newEvent.type,
        isRecurring: false,
        color: '#3B82F6',
      });

      setNewEvent({ name: '', date: '', type: 'birthday' });
      setShowAddModal(false);
      fetchUpcomingEvents('default-user', 10);

      Alert.alert('Success', 'Event added successfully!');
    } catch {
      Alert.alert('Error', 'Failed to add event');
    }
  };

  const handleDeleteEvent = (eventId: string, eventName: string) => {
    Alert.alert('Delete Event', `Are you sure you want to delete "${eventName}"?`, [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteEvent(eventId);
            fetchUpcomingEvents('default-user', 10);
          } catch {
            Alert.alert('Error', 'Failed to delete event');
          }
        },
      },
    ]);
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'birthday':
        return 'gift-outline';
      case 'anniversary':
        return 'heart-outline';
      case 'holiday':
        return 'star-outline';
      default:
        return 'calendar-outline';
    }
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'birthday':
        return '#F59E0B';
      case 'anniversary':
        return '#EF4444';
      case 'holiday':
        return '#10B981';
      default:
        return '#3B82F6';
    }
  };

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      <StatusBar style="auto" />

      {/* Header */}
      <View className="bg-white px-6 pb-6 pt-12 shadow-sm dark:bg-gray-800">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons
              name="arrow-back"
              size={24}
              color={resolvedTheme === 'dark' ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
          <View className="ml-4 flex-1">
            <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">Events</Text>
            <Text className="mt-1 text-gray-600 dark:text-gray-400">
              Countdown to special occasions
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => setShowAddModal(true)}
            className="h-10 w-10 items-center justify-center rounded-full bg-blue-500"
          >
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <ScrollView className="flex-1 px-6 pt-6">
        {eventsLoading ? (
          <View className="items-center justify-center py-12">
            <Text className="text-gray-600 dark:text-gray-400">Loading events...</Text>
          </View>
        ) : upcomingEvents.length === 0 ? (
          <View className="items-center justify-center py-12">
            <View className="mb-4 h-24 w-24 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
              <Ionicons name="calendar-outline" size={40} color="#3B82F6" />
            </View>
            <Text className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
              No Events Yet
            </Text>
            <Text className="mb-6 text-center text-gray-600 dark:text-gray-400">
              Add birthdays, anniversaries, and holidays to track countdown timers
            </Text>
            <TouchableOpacity
              onPress={() => setShowAddModal(true)}
              className="rounded-xl bg-blue-500 px-6 py-3"
            >
              <Text className="font-semibold text-white">Add Your First Event</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View className="space-y-4">
            {upcomingEvents.map(event => (
              <View key={event.id} className="rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800">
                <View className="flex-row items-center justify-between">
                  <View className="flex-1 flex-row items-center">
                    <View
                      className="mr-4 h-12 w-12 items-center justify-center rounded-full"
                      style={{ backgroundColor: `${getEventColor(event.type)}20` }}
                    >
                      <Ionicons
                        name={getEventIcon(event.type) as any}
                        size={24}
                        color={getEventColor(event.type)}
                      />
                    </View>
                    <View className="flex-1">
                      <Text className="font-semibold text-gray-900 dark:text-gray-100">
                        {event.name}
                      </Text>
                      <Text className="text-sm capitalize text-gray-600 dark:text-gray-400">
                        {event.type}
                      </Text>
                    </View>
                  </View>
                  <View className="mr-4 items-end">
                    <Text
                      className="text-3xl font-bold"
                      style={{ color: getEventColor(event.type) }}
                    >
                      {event.daysRemaining}
                    </Text>
                    <Text className="text-xs text-gray-500 dark:text-gray-400">
                      {event.daysRemaining === 0
                        ? 'Today!'
                        : event.daysRemaining === 1
                          ? 'day left'
                          : 'days left'}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleDeleteEvent(event.id, event.name)}
                    className="h-8 w-8 items-center justify-center"
                  >
                    <Ionicons name="trash-outline" size={18} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Add Event Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View className="flex-1 bg-gray-50 dark:bg-gray-900">
          <View className="bg-white px-6 pb-6 pt-12 shadow-sm dark:bg-gray-800">
            <View className="flex-row items-center justify-between">
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
              <Text className="text-xl font-bold text-gray-900 dark:text-gray-100">Add Event</Text>
              <TouchableOpacity
                onPress={handleAddEvent}
                className="rounded-lg bg-blue-500 px-4 py-2"
              >
                <Text className="font-medium text-white">Save</Text>
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView className="flex-1 p-6">
            <View className="space-y-6">
              {/* Event Name */}
              <View>
                <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
                  Event Name *
                </Text>
                <TextInput
                  value={newEvent.name}
                  onChangeText={text => setNewEvent(prev => ({ ...prev, name: text }))}
                  placeholder="e.g., John's Birthday"
                  placeholderTextColor="#9CA3AF"
                  className="rounded-xl border-2 border-gray-200 bg-white px-4 py-3 text-gray-900 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                />
              </View>

              {/* Event Date */}
              <View>
                <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
                  Date *
                </Text>
                <TextInput
                  value={newEvent.date}
                  onChangeText={text => setNewEvent(prev => ({ ...prev, date: text }))}
                  placeholder="YYYY-MM-DD"
                  placeholderTextColor="#9CA3AF"
                  className="rounded-xl border-2 border-gray-200 bg-white px-4 py-3 text-gray-900 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                />
              </View>

              {/* Event Type */}
              <View>
                <Text className="mb-2 text-base font-medium text-gray-900 dark:text-gray-100">
                  Type
                </Text>
                <View className="flex-row space-x-3">
                  {['birthday', 'anniversary', 'holiday'].map(type => (
                    <TouchableOpacity
                      key={type}
                      onPress={() => setNewEvent(prev => ({ ...prev, type: type as any }))}
                      className={`flex-1 rounded-xl border-2 py-3 ${
                        newEvent.type === type
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                          : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'
                      }`}
                    >
                      <Text
                        className={`text-center font-medium capitalize ${
                          newEvent.type === type
                            ? 'text-blue-600 dark:text-blue-400'
                            : 'text-gray-600 dark:text-gray-400'
                        }`}
                      >
                        {type}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

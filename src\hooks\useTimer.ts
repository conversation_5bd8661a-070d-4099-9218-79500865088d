import { useEffect, useRef } from 'react';
import { useTimerStore } from '../stores';

/**
 * Custom hook for timer management
 * Handles timer lifecycle and provides convenient timer operations
 */
export const useTimer = () => {
  const {
    type,
    status,
    timeRemaining,
    totalTime,
    pomodoroPhase,
    pomodoroSession,
    pomodoroCompletedSessions,
    startTimer,
    pauseTimer,
    resumeTimer,
    stopTimer,
    resetTimer,
    tick,
    startPomodoro,
    nextPomodoroPhase,
    resetPomodoro,
  } = useTimerStore();

  const intervalRef = useRef<number | null>(null);

  // Handle timer ticking
  useEffect(() => {
    if (status === 'running') {
      intervalRef.current = setInterval(() => {
        tick();
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [status, tick]);

  // Helper functions
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgress = () => {
    if (totalTime === 0) return 0;
    // Since we only have 'pomodoro' type, calculate progress normally
    return (totalTime - timeRemaining) / totalTime;
  };

  const getProgressPercentage = () => {
    return Math.round(getProgress() * 100);
  };

  const isRunning = () => status === 'running';
  const isPaused = () => status === 'paused';
  const isIdle = () => status === 'idle';
  const isCompleted = () => status === 'completed';

  const toggleTimer = () => {
    if (isRunning()) {
      pauseTimer();
    } else if (isPaused()) {
      resumeTimer();
    } else {
      if (type === 'pomodoro') {
        startPomodoro();
      } else {
        startTimer(type, type === 'countdown' ? 25 : undefined);
      }
    }
  };

  const getPhaseColor = () => {
    switch (pomodoroPhase) {
      case 'work':
        return '#EF4444'; // red
      case 'break':
        return '#10B981'; // green
      case 'longBreak':
        return '#3B82F6'; // blue
      default:
        return '#6B7280'; // gray
    }
  };

  const getPhaseText = () => {
    switch (pomodoroPhase) {
      case 'work':
        return 'Focus Time';
      case 'break':
        return 'Short Break';
      case 'longBreak':
        return 'Long Break';
      default:
        return 'Timer';
    }
  };

  const getRemainingMinutes = () => Math.floor(timeRemaining / 60);
  const getRemainingSeconds = () => timeRemaining % 60;

  return {
    // State
    type,
    status,
    timeRemaining,
    totalTime,
    pomodoroPhase,
    pomodoroSession,
    pomodoroCompletedSessions,

    // Actions
    startTimer,
    pauseTimer,
    resumeTimer,
    stopTimer,
    resetTimer,
    toggleTimer,
    startPomodoro,
    nextPomodoroPhase,
    resetPomodoro,

    // Helper functions
    formatTime,
    getProgress,
    getProgressPercentage,
    isRunning,
    isPaused,
    isIdle,
    isCompleted,
    getPhaseColor,
    getPhaseText,
    getRemainingMinutes,
    getRemainingSeconds,

    // Formatted values
    formattedTime: formatTime(timeRemaining),
    progressPercentage: getProgressPercentage(),
    phaseColor: getPhaseColor(),
    phaseText: getPhaseText(),
  };
};

export default useTimer;

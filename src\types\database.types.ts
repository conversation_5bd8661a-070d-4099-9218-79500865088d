/**
 * Type definitions for database-related operations and structures
 */

export interface DatabaseConnection {
  isConnected: boolean;
  version: number;
  lastMigration?: string;
}

export interface DatabaseMigration {
  version: number;
  name: string;
  up: string[];
  down: string[];
  executed_at?: string;
}

export interface QueryResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  rowsAffected?: number;
  insertId?: string;
}

export interface DatabaseError {
  code: string;
  message: string;
  query?: string;
  params?: any[];
  stack?: string;
}

export interface TransactionOptions {
  isolation_level?: 'READ_UNCOMMITTED' | 'READ_COMMITTED' | 'REPEATABLE_READ' | 'SERIALIZABLE';
  timeout?: number;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  offset?: number;
}

export interface SortOptions {
  field: string;
  direction: 'ASC' | 'DESC';
}

export interface FilterOptions {
  field: string;
  operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'LIKE' | 'IN' | 'NOT IN';
  value: any;
}

export interface QueryOptions {
  pagination?: PaginationOptions;
  sort?: SortOptions[];
  filters?: FilterOptions[];
  include?: string[];
  exclude?: string[];
}

export interface DatabaseStats {
  total_records: number;
  table_sizes: {
    table_name: string;
    record_count: number;
    size_bytes: number;
  }[];
  last_vacuum?: string;
  last_backup?: string;
  integrity_check: boolean;
}

export interface BackupOptions {
  include_data: boolean;
  include_schema: boolean;
  compress: boolean;
  encryption_key?: string;
}

export interface RestoreOptions {
  overwrite_existing: boolean;
  verify_integrity: boolean;
  decryption_key?: string;
}

export interface DatabaseConfig {
  name: string;
  version: number;
  auto_vacuum: boolean;
  journal_mode: 'DELETE' | 'TRUNCATE' | 'PERSIST' | 'MEMORY' | 'WAL' | 'OFF';
  synchronous: 'OFF' | 'NORMAL' | 'FULL' | 'EXTRA';
  cache_size: number;
  foreign_keys: boolean;
  busy_timeout: number;
}

export interface SyncConflict {
  id: string;
  table_name: string;
  record_id: string;
  local_data: any;
  remote_data: any;
  conflict_type: 'UPDATE_UPDATE' | 'UPDATE_DELETE' | 'DELETE_UPDATE';
  created_at: string;
  resolved: boolean;
  resolution?: 'LOCAL' | 'REMOTE' | 'MERGE';
}

export interface SyncLog {
  id: string;
  sync_type: 'FULL' | 'INCREMENTAL' | 'CONFLICT_RESOLUTION';
  started_at: string;
  completed_at?: string;
  status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  records_synced: number;
  conflicts_found: number;
  error_message?: string;
}

export interface DatabaseIndex {
  name: string;
  table_name: string;
  columns: string[];
  unique: boolean;
  partial?: string;
}

export interface DatabaseTrigger {
  name: string;
  table_name: string;
  event: 'INSERT' | 'UPDATE' | 'DELETE';
  timing: 'BEFORE' | 'AFTER' | 'INSTEAD OF';
  condition?: string;
  action: string;
}

export interface DatabaseView {
  name: string;
  definition: string;
  columns: {
    name: string;
    type: string;
  }[];
}

export interface DatabaseSchema {
  version: number;
  tables: {
    name: string;
    columns: {
      name: string;
      type: string;
      nullable: boolean;
      default?: any;
      primary_key: boolean;
      foreign_key?: {
        table: string;
        column: string;
      };
    }[];
    indexes: DatabaseIndex[];
    triggers: DatabaseTrigger[];
  }[];
  views: DatabaseView[];
  migrations: DatabaseMigration[];
}

import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { HabitStats } from '../../types/habit.types';

interface HabitStatsCardProps {
  stats: HabitStats;
  title?: string;
  compact?: boolean;
}

const HabitStatsCard: React.FC<HabitStatsCardProps> = ({
  stats,
  title = 'Statistics',
  compact = false,
}) => {
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return 'Never';
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getCompletionRateColor = (rate: number) => {
    if (rate >= 0.8) return 'text-green-600 dark:text-green-400';
    if (rate >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getStreakColor = (streak: number) => {
    if (streak >= 7) return 'text-orange-600 dark:text-orange-400';
    if (streak >= 3) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  if (compact) {
    return (
      <View className="rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800">
        <Text className="mb-3 text-base font-bold text-gray-900 dark:text-gray-100">{title}</Text>

        <View className="flex-row justify-between">
          <View className="flex-1 items-center">
            <View className="mb-1 h-8 w-8 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
              <Ionicons name="flame" size={16} color="#F97316" />
            </View>
            <Text className={`text-lg font-bold ${getStreakColor(stats.currentStreak)}`}>
              {stats.currentStreak}
            </Text>
            <Text className="text-xs text-gray-600 dark:text-gray-400">Current</Text>
          </View>

          <View className="flex-1 items-center">
            <View className="mb-1 h-8 w-8 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/20">
              <Ionicons name="trophy" size={16} color="#8B5CF6" />
            </View>
            <Text className="text-lg font-bold text-purple-600 dark:text-purple-400">
              {stats.longestStreak}
            </Text>
            <Text className="text-xs text-gray-600 dark:text-gray-400">Best</Text>
          </View>

          <View className="flex-1 items-center">
            <View className="mb-1 h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
              <Ionicons name="checkmark-circle" size={16} color="#3B82F6" />
            </View>
            <Text className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {stats.totalCompletions}
            </Text>
            <Text className="text-xs text-gray-600 dark:text-gray-400">Total</Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
      <Text className="mb-4 text-lg font-bold text-gray-900 dark:text-gray-100">{title}</Text>

      {/* Main Stats Row */}
      <View className="mb-6 flex-row justify-between">
        <View className="flex-1 items-center">
          <View className="mb-2 h-12 w-12 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
            <Ionicons name="flame" size={24} color="#F97316" />
          </View>
          <Text className={`text-2xl font-bold ${getStreakColor(stats.currentStreak)}`}>
            {stats.currentStreak}
          </Text>
          <Text className="text-sm text-gray-600 dark:text-gray-400">Current Streak</Text>
        </View>

        <View className="flex-1 items-center">
          <View className="mb-2 h-12 w-12 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/20">
            <Ionicons name="trophy" size={24} color="#8B5CF6" />
          </View>
          <Text className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {stats.longestStreak}
          </Text>
          <Text className="text-sm text-gray-600 dark:text-gray-400">Best Streak</Text>
        </View>

        <View className="flex-1 items-center">
          <View className="mb-2 h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
            <Ionicons name="checkmark-circle" size={24} color="#3B82F6" />
          </View>
          <Text className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {stats.totalCompletions}
          </Text>
          <Text className="text-sm text-gray-600 dark:text-gray-400">Total Completions</Text>
        </View>
      </View>

      {/* Additional Stats */}
      <View className="space-y-3 border-t border-gray-100 pt-4 dark:border-gray-700">
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <Ionicons name="trending-up" size={16} color="#6B7280" />
            <Text className="ml-2 text-sm text-gray-600 dark:text-gray-400">
              Completion Rate (30 days)
            </Text>
          </View>
          <Text className={`text-sm font-medium ${getCompletionRateColor(stats.completionRate)}`}>
            {Math.round(stats.completionRate * 100)}%
          </Text>
        </View>

        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <Ionicons name="calendar" size={16} color="#6B7280" />
            <Text className="ml-2 text-sm text-gray-600 dark:text-gray-400">This Week</Text>
          </View>
          <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {stats.averageCompletionsPerWeek} completions
          </Text>
        </View>

        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <Ionicons name="calendar-outline" size={16} color="#6B7280" />
            <Text className="ml-2 text-sm text-gray-600 dark:text-gray-400">This Month</Text>
          </View>
          <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {stats.averageCompletionsPerMonth} completions
          </Text>
        </View>

        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <Ionicons name="time" size={16} color="#6B7280" />
            <Text className="ml-2 text-sm text-gray-600 dark:text-gray-400">Last Completed</Text>
          </View>
          <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {formatDate(stats.lastCompletedDate)}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default HabitStatsCard;

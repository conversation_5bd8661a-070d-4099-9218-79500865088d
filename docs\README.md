# WinArc: Habit Tracker - Documentation

Welcome to the comprehensive documentation for WinArc: Habit Tracker, an
Expo-managed React Native mobile application designed to help users build
consistent daily routines through habit formation, time management, and
mindfulness practices.

## 📚 Documentation Overview

This documentation provides detailed information about the project's
architecture, features, development roadmap, and technical implementation.

### Core Documentation

#### [🏗️ Project Structure](./PROJECT_STRUCTURE.md)

- **Purpose**: Detailed overview of the project's folder organization and
  architecture
- **Contents**:
  - File and folder structure explanation
  - Architecture layers (App, Components, Data, State Management)
  - Development tools and workflow
  - Key technologies and their roles

#### [⚡ Technology Stack](./TECH_STACK.md)

- **Purpose**: Comprehensive technology choices and implementation details
- **Contents**:
  - Expo-managed React Native setup and rationale
  - UI framework comparison (NativeWind vs alternatives)
  - Database architecture (Expo SQLite + TypeORM)
  - State management with Zustand
  - Navigation with Expo Router
  - Development tools and CI/CD pipeline

#### [🎯 Features Specification](./FEATURES.md)

- **Purpose**: Complete feature requirements and user stories
- **Contents**:
  - Core habit management system with hierarchical structure
  - Enhanced Pomodoro timer functionality
  - Event countdown system for important dates
  - Breathing exercises and mindfulness features (planned)
  - Technical requirements and platform specifications
  - Feature priority matrix and success metrics

#### [🗺️ Development Roadmap](./ROADMAP.md)

- **Purpose**: Detailed development phases, timelines, and milestones
- **Contents**:
  - Phase-by-phase development plan
  - Technical implementation details
  - Success criteria and metrics
  - Resource requirements and budget estimates
  - Risk management strategies

## 🚀 Quick Start

For immediate setup and development instructions, see the main
[README.md](../README.md) in the project root.

## 🏗️ Project Architecture

WinArc is built using modern React Native development practices with Expo's
managed workflow:

- **Framework**: Expo-managed React Native (SDK ~53.0)
- **Language**: TypeScript with full type coverage
- **Styling**: NativeWind v4 (Tailwind CSS for React Native)
- **Navigation**: Expo Router (file-based routing)
- **State Management**: Zustand
- **Database**: Expo SQLite + TypeORM
- **Development Tools**: ESLint, Prettier, TypeScript

## 📱 Core Features

### Phase 1 (MVP) - Completed ✅

- Core habit management (creation, tracking, completion)
- Hierarchical habit structure with sub-tasks and purpose fields
- Progress tracking with streaks and completion history
- Pomodoro timer with work/break phases
- Event countdown system for important dates
- Light/dark theme support
- Local data storage with offline support
- Android compatibility with TypeORM
- Expo-managed development workflow

### Phase 2 (In Development) 🚧

- Time range picker with DateTimePicker integration
- Enhanced analytics and insights
- Smart notifications and reminders
- Breathing exercises and mindfulness tools

### Phase 3 (Planned) 📋

- Social features and accountability partners
- Wellness integration and mood tracking
- Brain dump and journaling capabilities
- Habit-based alarm system

## 🛠️ Development Workflow

### Prerequisites

- Node.js 18+ and npm/yarn
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (macOS) or Android Emulator
- VS Code with recommended extensions

### Getting Started

```bash
# Install dependencies
npm install

# Start development server
npx expo start

# Run on specific platform
npx expo start --ios
npx expo start --android
```

### Development Commands

```bash
# Code quality
npm run lint          # Run ESLint
npm run lint:fix       # Fix ESLint issues
npm run format         # Format with Prettier
npm run type-check     # TypeScript checking
npm run dev:tools      # Run all quality checks

# Build and deployment
npx expo build         # Build for app stores
npx expo publish       # Publish updates
```

## 📖 Additional Resources

### External Documentation

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [NativeWind Documentation](https://www.nativewind.dev/)
- [TypeORM Documentation](https://typeorm.io/)
- [Zustand Documentation](https://github.com/pmndrs/zustand)

### Community and Support

- [Expo Discord Community](https://chat.expo.dev/)
- [React Native Community](https://reactnative.dev/community/overview)
- [Stack Overflow - React Native](https://stackoverflow.com/questions/tagged/react-native)

## 🤝 Contributing

This project follows standard React Native and Expo development practices. Key
guidelines:

1. **Code Style**: Follow ESLint and Prettier configurations
2. **TypeScript**: Maintain full type coverage
3. **Testing**: Write tests for critical functionality
4. **Documentation**: Update docs when adding features
5. **Git Workflow**: Use conventional commits and feature branches

## 📄 License

This project is private and proprietary. All rights reserved.

---

**Last Updated**: July 2025  
**Version**: 1.0.0  
**Expo SDK**: ~53.0  
**React Native**: 0.79+

import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { Habit } from '../../database/entities/Habit';
import { HabitCompletion } from '../../database/entities/HabitCompletion';
import HabitCard from './HabitCard';

interface HabitListProps {
  habits: (Habit & { completions?: HabitCompletion[] })[];
  onToggleCompletion: (habitId: string) => void;
  onHabitPress?: (habit: Habit) => void;
  onHabitLongPress?: (habit: Habit) => void;
  onCreateHabit?: () => void;
  isLoading?: boolean;
  compact?: boolean;
  showProgress?: boolean;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  emptyStateIcon?: keyof typeof Ionicons.glyphMap;
  // Selection mode props
  selectionMode?: boolean;
  selectedHabits?: Habit[];
  onToggleSelection?: (habit: Habit) => void;
  onEnterSelectionMode?: () => void;
  onExitSelectionMode?: () => void;
}

const HabitList: React.FC<HabitListProps> = ({
  habits,
  onToggleCompletion,
  onHabitPress,
  onHabitLongPress,
  onCreateHabit,
  isLoading = false,
  compact = false,
  showProgress = true,
  emptyStateTitle = 'No habits yet',
  emptyStateDescription = 'Start building your routine by creating your first habit',
  emptyStateIcon = 'leaf-outline',
  // Selection mode props
  selectionMode = false,
  selectedHabits = [],
  onToggleSelection,
  onEnterSelectionMode,
  onExitSelectionMode,
}) => {
  const renderHabitCard = ({ item }: { item: Habit & { completions?: HabitCompletion[] } }) => (
    <HabitCard
      habit={item}
      onToggleCompletion={onToggleCompletion}
      onPress={onHabitPress}
      onLongPress={onHabitLongPress}
      compact={compact}
      showProgress={showProgress}
      selectionMode={selectionMode}
      isSelected={selectedHabits.some(h => h.id === item.id)}
      onToggleSelection={onToggleSelection}
    />
  );

  const renderEmptyState = () => (
    <View className="flex-1 items-center justify-center py-20">
      <Ionicons name={emptyStateIcon} size={64} color="#9CA3AF" />
      <Text className="mt-4 text-xl font-semibold text-gray-900 dark:text-gray-100">
        {emptyStateTitle}
      </Text>
      <Text className="mt-2 px-8 text-center text-gray-600 dark:text-gray-400">
        {emptyStateDescription}
      </Text>
      {onCreateHabit && (
        <TouchableOpacity onPress={onCreateHabit} className="mt-6 rounded-lg bg-blue-500 px-6 py-3">
          <Text className="font-semibold text-white">Create First Habit</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderLoadingState = () => (
    <View className="flex-1 items-center justify-center py-20">
      <View className="animate-spin">
        <Ionicons name="refresh" size={32} color="#6B7280" />
      </View>
      <Text className="mt-4 text-gray-600 dark:text-gray-400">Loading habits...</Text>
    </View>
  );

  if (isLoading) {
    return renderLoadingState();
  }

  if (habits.length === 0) {
    return renderEmptyState();
  }

  return (
    <FlatList
      data={habits}
      renderItem={renderHabitCard}
      keyExtractor={item => item.id}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingBottom: 20,
        paddingHorizontal: compact ? 0 : 16,
      }}
      ItemSeparatorComponent={compact ? () => <View className="h-2" /> : undefined}
    />
  );
};

export default HabitList;

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { databaseService } from '../database';
import { Habit, HabitCompletion } from '../database/entities';

interface HabitState {
  habits: Habit[];
  selectedHabit: Habit | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  loadHabits: (userId: string) => Promise<void>;
  createHabit: (habitData: Partial<Habit>) => Promise<void>;
  updateHabit: (habitId: string, habitData: Partial<Habit>) => Promise<void>;
  deleteHabit: (habitId: string) => Promise<void>;
  setSelectedHabit: (habit: Habit | null) => void;
  reorderHabits: (habitIds: string[]) => Promise<void>;

  // Completion actions
  completeHabit: (
    habitId: string,
    date: string,
    count?: number,
    notes?: string,
    moodRating?: number
  ) => Promise<void>;
  uncompleteHabit: (habitId: string, date: string) => Promise<void>;
  getHabitCompletions: (
    habitId: string,
    startDate?: string,
    endDate?: string
  ) => Promise<HabitCompletion[]>;

  // Streak actions
  getCurrentStreak: (habitId: string) => Promise<number>;
  getLongestStreak: (habitId: string) => Promise<number>;

  clearError: () => void;
  refreshHabits: (userId: string) => Promise<void>;
}

export const useHabitStore = create<HabitState>()(
  devtools(
    (set, get) => ({
      habits: [],
      selectedHabit: null,
      isLoading: false,
      error: null,

      loadHabits: async userId => {
        set({ isLoading: true, error: null }, false, 'loadHabits/start');

        try {
          const habits = await databaseService.habitRepository.findByUserId(userId);
          set(
            {
              habits,
              isLoading: false,
            },
            false,
            'loadHabits/success'
          );
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to load habits',
              isLoading: false,
            },
            false,
            'loadHabits/error'
          );
        }
      },

      createHabit: async habitData => {
        set({ isLoading: true, error: null }, false, 'createHabit/start');

        try {
          const newHabit = await databaseService.habitRepository.create(habitData);
          const currentHabits = get().habits;

          set(
            {
              habits: [...currentHabits, newHabit],
              isLoading: false,
            },
            false,
            'createHabit/success'
          );
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to create habit',
              isLoading: false,
            },
            false,
            'createHabit/error'
          );
        }
      },

      updateHabit: async (habitId, habitData) => {
        set({ isLoading: true, error: null }, false, 'updateHabit/start');

        try {
          const updatedHabit = await databaseService.habitRepository.update(habitId, habitData);

          if (updatedHabit) {
            const currentHabits = get().habits;
            const updatedHabits = currentHabits.map(habit =>
              habit.id === habitId ? updatedHabit : habit
            );

            set(
              {
                habits: updatedHabits,
                selectedHabit:
                  get().selectedHabit?.id === habitId ? updatedHabit : get().selectedHabit,
                isLoading: false,
              },
              false,
              'updateHabit/success'
            );
          }
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to update habit',
              isLoading: false,
            },
            false,
            'updateHabit/error'
          );
        }
      },

      deleteHabit: async habitId => {
        set({ isLoading: true, error: null }, false, 'deleteHabit/start');

        try {
          await databaseService.habitRepository.softDelete(habitId);
          const currentHabits = get().habits;
          const filteredHabits = currentHabits.filter(habit => habit.id !== habitId);

          set(
            {
              habits: filteredHabits,
              selectedHabit: get().selectedHabit?.id === habitId ? null : get().selectedHabit,
              isLoading: false,
            },
            false,
            'deleteHabit/success'
          );
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to delete habit',
              isLoading: false,
            },
            false,
            'deleteHabit/error'
          );
        }
      },

      setSelectedHabit: habit => {
        set({ selectedHabit: habit }, false, 'setSelectedHabit');
      },

      reorderHabits: async habitIds => {
        set({ isLoading: true, error: null }, false, 'reorderHabits/start');

        try {
          // Update sort order for each habit
          await Promise.all(
            habitIds.map((habitId, index) =>
              databaseService.habitRepository.updateSortOrder(habitId, index)
            )
          );

          // Reload habits to reflect new order
          const currentHabits = get().habits;
          const reorderedHabits = habitIds
            .map(id => currentHabits.find(habit => habit.id === id)!)
            .filter(Boolean);

          set(
            {
              habits: reorderedHabits,
              isLoading: false,
            },
            false,
            'reorderHabits/success'
          );
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to reorder habits',
              isLoading: false,
            },
            false,
            'reorderHabits/error'
          );
        }
      },

      completeHabit: async (habitId, date, count = 1, notes, moodRating) => {
        try {
          await databaseService.habitCompletionRepository.create({
            habit_id: habitId,
            completed_date: date,
            count,
            notes,
            mood_rating: moodRating,
          });

          // Refresh habits to update completion data
          const userId = get().habits.find(h => h.id === habitId)?.user_id;
          if (userId) {
            await get().refreshHabits(userId);
          }
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to complete habit',
            },
            false,
            'completeHabit/error'
          );
        }
      },

      uncompleteHabit: async (habitId, date) => {
        try {
          await databaseService.habitCompletionRepository.deleteByHabitAndDate(habitId, date);

          // Refresh habits to update completion data
          const userId = get().habits.find(h => h.id === habitId)?.user_id;
          if (userId) {
            await get().refreshHabits(userId);
          }
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to uncomplete habit',
            },
            false,
            'uncompleteHabit/error'
          );
        }
      },

      getHabitCompletions: async (habitId, startDate, endDate) => {
        try {
          if (startDate && endDate) {
            return await databaseService.habitCompletionRepository.findByDateRange(
              habitId,
              startDate,
              endDate
            );
          } else {
            return await databaseService.habitCompletionRepository.findByHabitId(habitId);
          }
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to get habit completions',
            },
            false,
            'getHabitCompletions/error'
          );
          return [];
        }
      },

      getCurrentStreak: async habitId => {
        try {
          return await databaseService.habitStreakRepository.getCurrentStreakLength(habitId);
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to get current streak',
            },
            false,
            'getCurrentStreak/error'
          );
          return 0;
        }
      },

      getLongestStreak: async habitId => {
        try {
          return await databaseService.habitStreakRepository.getLongestStreak(habitId);
        } catch (error) {
          set(
            {
              error: error instanceof Error ? error.message : 'Failed to get longest streak',
            },
            false,
            'getLongestStreak/error'
          );
          return 0;
        }
      },

      clearError: () => {
        set({ error: null }, false, 'clearError');
      },

      refreshHabits: async userId => {
        await get().loadHabits(userId);
      },
    }),
    {
      name: 'habit-store',
    }
  )
);

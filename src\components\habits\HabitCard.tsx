import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { Habit } from '../../database/entities/Habit';
import { HabitCompletion } from '../../database/entities/HabitCompletion';

interface HabitCardProps {
  habit: Habit & { completions?: HabitCompletion[] };
  onToggleCompletion: (habitId: string) => void;
  onPress?: (habit: Habit) => void;
  onLongPress?: (habit: Habit) => void;
  showProgress?: boolean;
  compact?: boolean;
  // Selection mode props
  selectionMode?: boolean;
  isSelected?: boolean;
  onToggleSelection?: (habit: Habit) => void;
}

const HabitCard: React.FC<HabitCardProps> = ({
  habit,
  onToggleCompletion,
  onPress,
  onLongPress,
  showProgress = true,
  compact = false,
  // Selection mode props
  selectionMode = false,
  isSelected = false,
  onToggleSelection,
}) => {
  const today = new Date().toISOString().split('T')[0];

  const isCompletedToday =
    habit.completions?.some(completion => completion.completed_date === today) || false;

  const todayCompletion = habit.completions?.find(
    completion => completion.completed_date === today
  );

  const completionCount = todayCompletion?.count || 0;
  const progressPercentage = Math.min((completionCount / habit.target_count) * 100, 100);

  // Calculate current streak (simplified - in real app this would come from HabitStreak entity)
  const getCurrentStreak = (): number => {
    if (!habit.completions || habit.completions.length === 0) return 0;

    const sortedCompletions = habit.completions.sort(
      (a, b) => new Date(b.completed_date).getTime() - new Date(a.completed_date).getTime()
    );

    let streak = 0;
    let currentDate = new Date();

    for (const completion of sortedCompletions) {
      const completionDate = new Date(completion.completed_date);
      const daysDiff = Math.floor(
        (currentDate.getTime() - completionDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysDiff === streak) {
        streak++;
        currentDate = completionDate;
      } else {
        break;
      }
    }

    return streak;
  };

  const currentStreak = getCurrentStreak();

  const handlePress = () => {
    if (selectionMode && onToggleSelection) {
      onToggleSelection(habit);
    } else if (onPress) {
      onPress(habit);
    }
  };

  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress(habit);
    }
  };

  if (compact) {
    return (
      <TouchableOpacity
        onPress={handlePress}
        onLongPress={handleLongPress}
        className="flex-row items-center rounded-xl bg-white p-3 shadow-sm dark:bg-gray-800"
      >
        <View className="mr-3 h-3 w-3 rounded-full" style={{ backgroundColor: habit.color }} />
        <View className="flex-1">
          <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">{habit.name}</Text>
          <Text className="text-xs text-gray-600 dark:text-gray-400">{habit.category}</Text>
        </View>
        <TouchableOpacity
          onPress={() => onToggleCompletion(habit.id)}
          className={`h-6 w-6 items-center justify-center rounded-full border-2 ${
            isCompletedToday
              ? 'border-green-500 bg-green-500'
              : 'border-gray-300 dark:border-gray-600'
          }`}
        >
          {isCompletedToday && <Ionicons name="checkmark" size={12} color="white" />}
        </TouchableOpacity>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={handlePress}
      onLongPress={handleLongPress}
      className={`mb-3 rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800 ${
        selectionMode && isSelected ? 'border-2 border-blue-500' : ''
      }`}
    >
      <View className="flex-row items-start justify-between">
        {/* Selection indicator */}
        {selectionMode && (
          <View className="mr-3 mt-1">
            <View
              className={`h-6 w-6 items-center justify-center rounded-full border-2 ${
                isSelected ? 'border-blue-500 bg-blue-500' : 'border-gray-300 dark:border-gray-600'
              }`}
            >
              {isSelected && <Ionicons name="checkmark" size={16} color="white" />}
            </View>
          </View>
        )}

        <View className="mr-4 flex-1">
          {/* Header */}
          <View className="mb-2 flex-row items-center">
            <View className="mr-3 h-4 w-4 rounded-full" style={{ backgroundColor: habit.color }} />
            <Text className="flex-1 text-lg font-semibold text-gray-900 dark:text-gray-100">
              {habit.name}
            </Text>
          </View>

          {/* Description */}
          {habit.description && (
            <Text className="mb-2 text-sm text-gray-600 dark:text-gray-400">
              {habit.description}
            </Text>
          )}

          {/* Stats Row */}
          <View className="mb-2 flex-row items-center space-x-4">
            <View className="flex-row items-center">
              <Ionicons name="flame" size={16} color="#F97316" />
              <Text className="ml-1 text-sm text-gray-600 dark:text-gray-400">
                {currentStreak} day streak
              </Text>
            </View>
            <View className="flex-row items-center">
              <Ionicons name="calendar" size={16} color="#6B7280" />
              <Text className="ml-1 text-sm capitalize text-gray-600 dark:text-gray-400">
                {habit.frequency}
              </Text>
            </View>
          </View>

          {/* Progress */}
          {showProgress && (
            <View className="mb-2">
              <View className="mb-1 flex-row items-center justify-between">
                <Text className="text-sm text-gray-600 dark:text-gray-400">
                  Progress: {completionCount}/{habit.target_count} {habit.unit}
                </Text>
                <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {Math.round(progressPercentage)}%
                </Text>
              </View>
              <View className="h-2 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                <View
                  className="h-full rounded-full bg-green-500 transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </View>
            </View>
          )}
        </View>

        {/* Completion Button */}
        <TouchableOpacity
          onPress={() => onToggleCompletion(habit.id)}
          className={`h-12 w-12 items-center justify-center rounded-full border-2 ${
            isCompletedToday
              ? 'border-green-500 bg-green-500'
              : 'border-gray-300 dark:border-gray-600'
          }`}
        >
          {isCompletedToday && <Ionicons name="checkmark" size={24} color="white" />}
        </TouchableOpacity>
      </View>

      {/* Footer */}
      <View className="mt-4 border-t border-gray-100 pt-4 dark:border-gray-700">
        <View className="flex-row items-center justify-between">
          <Text className="text-sm text-gray-600 dark:text-gray-400">
            Target: {habit.target_count} {habit.unit} {habit.frequency}
          </Text>
          <TouchableOpacity>
            <Ionicons name="ellipsis-horizontal" size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default HabitCard;

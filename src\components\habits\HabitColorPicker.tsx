import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface HabitColorPickerProps {
  selectedColor: string;
  onColorSelect: (color: string) => void;
  label?: string;
}

const HABIT_COLORS = [
  '#3B82F6', // Blue
  '#EF4444', // Red
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#8B5CF6', // Purple
  '#F97316', // Orange
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
  '#14B8A6', // Teal
  '#F43F5E', // Rose
];

const HabitColorPicker: React.FC<HabitColorPickerProps> = ({
  selectedColor,
  onColorSelect,
  label = 'Color',
}) => {
  return (
    <View className="mb-4">
      <Text className="mb-3 text-base font-medium text-gray-900 dark:text-gray-100">{label}</Text>
      <View className="flex-row flex-wrap gap-3">
        {HABIT_COLORS.map(color => (
          <TouchableOpacity
            key={color}
            onPress={() => onColorSelect(color)}
            className={`h-12 w-12 items-center justify-center rounded-full border-2 ${
              selectedColor === color
                ? 'border-gray-900 dark:border-gray-100'
                : 'border-gray-200 dark:border-gray-700'
            }`}
            style={{ backgroundColor: color }}
          >
            {selectedColor === color && <Ionicons name="checkmark" size={20} color="white" />}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default HabitColorPicker;

import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { ScrollView, Text, View } from 'react-native';
import { HabitAnalytics } from '../../types/habit.types';

interface HabitAnalyticsDashboardProps {
  analytics: HabitAnalytics;
  title?: string;
}

const HabitAnalyticsDashboard: React.FC<HabitAnalyticsDashboardProps> = ({
  analytics,
  title = 'Analytics Overview',
}) => {
  const getCompletionRateColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600 dark:text-green-400';
    if (rate >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, keyof typeof Ionicons.glyphMap> = {
      health: 'heart',
      fitness: 'barbell',
      productivity: 'briefcase',
      learning: 'book',
      mindfulness: 'leaf',
      social: 'people',
      creativity: 'color-palette',
      finance: 'card',
      other: 'ellipse',
    };
    return icons[category] || 'ellipse';
  };

  return (
    <ScrollView className="flex-1">
      <View className="space-y-6 p-6">
        {/* Header */}
        <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">{title}</Text>

        {/* Overview Cards */}
        <View className="flex-row space-x-4">
          <View className="flex-1 rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800">
            <View className="mb-2 flex-row items-center justify-between">
              <Ionicons name="list" size={20} color="#3B82F6" />
              <Text className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {analytics.activeHabits}
              </Text>
            </View>
            <Text className="text-sm text-gray-600 dark:text-gray-400">Active Habits</Text>
          </View>

          <View className="flex-1 rounded-xl bg-white p-4 shadow-sm dark:bg-gray-800">
            <View className="mb-2 flex-row items-center justify-between">
              <Ionicons name="checkmark-circle" size={20} color="#10B981" />
              <Text className="text-2xl font-bold text-green-600 dark:text-green-400">
                {analytics.completedToday}
              </Text>
            </View>
            <Text className="text-sm text-gray-600 dark:text-gray-400">Completed Today</Text>
          </View>
        </View>

        {/* Completion Rate */}
        <View className="rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
          <View className="mb-4 flex-row items-center justify-between">
            <Text className="text-lg font-bold text-gray-900 dark:text-gray-100">
              Today&apos;s Progress
            </Text>
            <Text
              className={`text-2xl font-bold ${getCompletionRateColor(analytics.completionRateToday)}`}
            >
              {analytics.completionRateToday}%
            </Text>
          </View>

          {/* Progress Bar */}
          <View className="h-3 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
            <View
              className="h-full rounded-full bg-gradient-to-r from-blue-500 to-green-500"
              style={{ width: `${analytics.completionRateToday}%` }}
            />
          </View>

          <Text className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {analytics.completedToday} of {analytics.activeHabits} habits completed
          </Text>
        </View>

        {/* Top Performers */}
        {(analytics.mostCompletedHabit || analytics.longestStreakHabit) && (
          <View className="rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
            <Text className="mb-4 text-lg font-bold text-gray-900 dark:text-gray-100">
              Top Performers
            </Text>

            <View className="space-y-4">
              {analytics.mostCompletedHabit && (
                <View className="flex-row items-center justify-between">
                  <View className="flex-1 flex-row items-center">
                    <View className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
                      <Ionicons name="trophy" size={20} color="#3B82F6" />
                    </View>
                    <View className="flex-1">
                      <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Most Completed
                      </Text>
                      <Text className="text-xs text-gray-600 dark:text-gray-400">
                        {analytics.mostCompletedHabit.name}
                      </Text>
                    </View>
                  </View>
                  <Text className="text-lg font-bold text-blue-600 dark:text-blue-400">
                    {analytics.mostCompletedHabit.completions}
                  </Text>
                </View>
              )}

              {analytics.longestStreakHabit && (
                <View className="flex-row items-center justify-between">
                  <View className="flex-1 flex-row items-center">
                    <View className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
                      <Ionicons name="flame" size={20} color="#F97316" />
                    </View>
                    <View className="flex-1">
                      <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Longest Streak
                      </Text>
                      <Text className="text-xs text-gray-600 dark:text-gray-400">
                        {analytics.longestStreakHabit.name}
                      </Text>
                    </View>
                  </View>
                  <Text className="text-lg font-bold text-orange-600 dark:text-orange-400">
                    {analytics.longestStreakHabit.streak}
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Category Breakdown */}
        {analytics.categoryBreakdown.length > 0 && (
          <View className="rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
            <Text className="mb-4 text-lg font-bold text-gray-900 dark:text-gray-100">
              Categories
            </Text>

            <View className="space-y-3">
              {analytics.categoryBreakdown.map((item, index) => (
                <View key={index} className="flex-row items-center justify-between">
                  <View className="flex-1 flex-row items-center">
                    <View className="mr-3 h-8 w-8 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
                      <Ionicons name={getCategoryIcon(item.category)} size={16} color="#6B7280" />
                    </View>
                    <Text className="flex-1 text-sm font-medium capitalize text-gray-900 dark:text-gray-100">
                      {item.category}
                    </Text>
                  </View>

                  <View className="flex-row items-center">
                    <Text className="mr-2 text-sm text-gray-600 dark:text-gray-400">
                      {item.count}
                    </Text>
                    <View className="h-2 w-16 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                      <View
                        className="h-full rounded-full bg-blue-500"
                        style={{ width: `${item.percentage}%` }}
                      />
                    </View>
                    <Text className="ml-2 w-8 text-xs text-gray-600 dark:text-gray-400">
                      {item.percentage}%
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Average Streak */}
        <View className="rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <View className="mr-4 h-12 w-12 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/20">
                <Ionicons name="stats-chart" size={24} color="#8B5CF6" />
              </View>
              <View>
                <Text className="text-lg font-bold text-gray-900 dark:text-gray-100">
                  Average Streak
                </Text>
                <Text className="text-sm text-gray-600 dark:text-gray-400">
                  Across all active habits
                </Text>
              </View>
            </View>
            <Text className="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {analytics.averageStreakLength}
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default HabitAnalyticsDashboard;
